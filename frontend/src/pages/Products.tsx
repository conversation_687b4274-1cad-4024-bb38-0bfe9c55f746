import React, { useEffect, useState, useRef, useCallback } from 'react';
import useProductStore from '../stores/productStore';
import useProductAnalyticsStore from '../stores/productAnalyticsStore';
import ProductForm from '../components/ProductForm';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaSearch,
  FaFilter,
  FaArrowLeft,
  FaSync,
  FaExclamationTriangle,
  FaChevronLeft,
  FaChevronRight,
  FaBox,
  FaCoins,
  FaChartLine,
  FaTrophy,
  FaExclamationCircle,
  FaWarehouse,
  FaCalendarAlt,
  FaDownload,
  FaMedal,
  FaStar,
  FaFire,
  FaCrown
} from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';
import { SelectInput } from '../components/inputs';
import ToggleSwitch from '../components/ToggleSwitch';
import Modal from '../components/Modal';
import DeleteConfirmModal from '../components/DeleteConfirmModal';
import SuccessModal from '../components/SuccessModal';
import Pagination from '../components/Pagination';
import DynamicSearchInput from '../components/DynamicSearchInput';
import DynamicPeriodSelector from '../components/analytics/DynamicPeriodSelector';
import useSearchSuggestions from '../hooks/useSearchSuggestions';

import FormattedCurrency from '../components/FormattedCurrency';

interface ProductsProps {
  isNew?: boolean;
}

const Products: React.FC<ProductsProps> = ({ isNew = false }) => {
  const {
    products,
    categories,
    loading,
    error,
    filters,
    selectedProduct,
    pagination,
    fetchProducts,
    fetchCategories,
    deleteProduct,
    setFilters,
    setSelectedProduct,
    nextPage,
    prevPage,
    setPage,
    setLimit
  } = useProductStore();

  const {
    analytics,
    bestSellingProducts,
    unsoldProducts,
    expectedLosses,
    inventoryStatus,
    performanceAnalysis,

    initialLoading: analyticsInitialLoading,
    loadingSteps: analyticsLoadingSteps,
    error: analyticsError,
    selectedPeriod,
    selectedCategory: analyticsCategory,
    selectedRiskLevel,
    fetchAnalyticsSummaryOnly,
    fetchBestSellingProducts,
    fetchUnsoldProducts,
    fetchExpectedLosses,
    fetchInventoryStatus,
    fetchPerformanceAnalysis,
    fetchAvailablePeriods,
    setPeriod,
    clearError: clearAnalyticsError
  } = useProductAnalyticsStore();

  useTheme(); // Usar el hook para asegurar que los estilos dark mode se apliquen correctamente
  const navigate = useNavigate();
  const location = useLocation();

  // Search suggestions hook
  const { getFilteredSuggestions } = useSearchSuggestions();

  // Filter functions for analytics data
  const getFilteredBestSelling = () => {
    if (!bestSellingProducts.length) return [];

    let filtered = [...bestSellingProducts];
    const filters = analyticsFilters.bestselling;

    if (filters.minSales) {
      filtered = filtered.filter(p => p.total_sold >= parseInt(filters.minSales));
    }
    if (filters.minRevenue) {
      filtered = filtered.filter(p => p.total_revenue >= parseFloat(filters.minRevenue));
    }
    if (filters.category) {
      filtered = filtered.filter(p => p.category === filters.category);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'total_revenue': return b.total_revenue - a.total_revenue;
        case 'total_profit': return b.total_profit - a.total_profit;
        case 'profit_margin': return b.profit_margin - a.profit_margin;
        default: return b.total_sold - a.total_sold;
      }
    });

    return filtered;
  };

  const getFilteredUnsold = () => {
    if (!unsoldProducts.length) return { data: [], total: 0, totalPages: 0 };

    let filtered = [...unsoldProducts];
    const filters = analyticsFilters.unsold;

    if (filters.minDaysInStock) {
      filtered = filtered.filter(p => p.days_in_stock >= parseInt(filters.minDaysInStock));
    }
    if (filters.maxStockValue) {
      filtered = filtered.filter(p => p.stock_value <= parseFloat(filters.maxStockValue));
    }
    if (filters.category) {
      filtered = filtered.filter(p => p.category === filters.category);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'stock_value': return b.stock_value - a.stock_value;
        case 'potential_loss': return b.potential_loss - a.potential_loss;
        case 'current_stock': return b.current_stock - a.current_stock;
        default: return b.days_in_stock - a.days_in_stock;
      }
    });

    // Pagination
    const { currentPage, itemsPerPage } = analyticsPagination.unsold;
    const total = filtered.length;
    const totalPages = Math.ceil(total / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const data = filtered.slice(startIndex, endIndex);

    return { data, total, totalPages };
  };

  const getFilteredLosses = () => {
    if (!expectedLosses.length) return { data: [], total: 0, totalPages: 0 };

    let filtered = [...expectedLosses];
    const filters = analyticsFilters.losses;

    if (filters.riskLevel) {
      filtered = filtered.filter(p => p.loss_category === filters.riskLevel);
    }
    if (filters.minLossAmount) {
      filtered = filtered.filter(p => p.estimated_loss_amount >= parseFloat(filters.minLossAmount));
    }
    if (filters.category) {
      filtered = filtered.filter(p => p.category === filters.category);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'days_without_sales': return b.days_without_sales - a.days_without_sales;
        case 'current_stock': return b.current_stock - a.current_stock;
        default: return b.estimated_loss_amount - a.estimated_loss_amount;
      }
    });

    // Pagination
    const { currentPage, itemsPerPage } = analyticsPagination.losses;
    const total = filtered.length;
    const totalPages = Math.ceil(total / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const data = filtered.slice(startIndex, endIndex);

    return { data, total, totalPages };
  };

  const getFilteredInventory = () => {
    if (!inventoryStatus.length) return { data: [], total: 0, totalPages: 0 };

    let filtered = [...inventoryStatus];
    const filters = analyticsFilters.inventory;

    if (filters.stockStatus) {
      filtered = filtered.filter(p => p.stock_status === filters.stockStatus);
    }
    if (filters.needsReorder) {
      filtered = filtered.filter(p => p.suggested_order_quantity > 0);
    }
    if (filters.category) {
      filtered = filtered.filter(p => p.category === filters.category);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'days_of_supply': return a.days_of_supply - b.days_of_supply;
        case 'suggested_order_quantity': return b.suggested_order_quantity - a.suggested_order_quantity;
        case 'reorder_point': return b.reorder_point - a.reorder_point;
        default: return a.current_stock - b.current_stock;
      }
    });

    // Pagination
    const { currentPage, itemsPerPage } = analyticsPagination.inventory;
    const total = filtered.length;
    const totalPages = Math.ceil(total / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const data = filtered.slice(startIndex, endIndex);

    return { data, total, totalPages };
  };

  const getFilteredPerformance = () => {
    if (!performanceAnalysis.length) return { data: [], total: 0, totalPages: 0 };

    let filtered = [...performanceAnalysis];
    const filters = analyticsFilters.performance;

    if (filters.minScore) {
      filtered = filtered.filter(p => p.performance_score >= parseInt(filters.minScore));
    }
    if (filters.salesTrend) {
      filtered = filtered.filter(p => p.sales_trend === filters.salesTrend);
    }
    if (filters.category) {
      filtered = filtered.filter(p => p.category === filters.category);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'total_sold': return b.total_sold - a.total_sold;
        case 'total_revenue': return b.total_revenue - a.total_revenue;
        case 'total_profit': return b.total_profit - a.total_profit;
        default: return b.performance_score - a.performance_score;
      }
    });

    // Pagination
    const { currentPage, itemsPerPage } = analyticsPagination.performance;
    const total = filtered.length;
    const totalPages = Math.ceil(total / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const data = filtered.slice(startIndex, endIndex);

    return { data, total, totalPages };
  };


  const [showForm, setShowForm] = useState(isNew);
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [selectedCategory, setSelectedCategory] = useState(filters.category || '');
  const [showLowStock, setShowLowStock] = useState(filters.lowStock || false);
  const [showZeroStock, setShowZeroStock] = useState(filters.zeroStock || false);
  const [isActive, setIsActive] = useState<boolean | undefined>(filters.isActive);
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState<'products' | 'analytics'>('products');
  const [analyticsTab, setAnalyticsTab] = useState<'overview' | 'bestselling' | 'unsold' | 'losses' | 'inventory' | 'performance'>('overview');
  const [analyticsSearchTerm, setAnalyticsSearchTerm] = useState('');

  // Analytics filters
  const [analyticsFilters, setAnalyticsFilters] = useState({
    bestselling: {
      minSales: '',
      minRevenue: '',
      category: '',
      sortBy: 'total_sold'
    },
    unsold: {
      minDaysInStock: '',
      maxStockValue: '',
      category: '',
      sortBy: 'days_in_stock'
    },
    losses: {
      riskLevel: '',
      minLossAmount: '',
      category: '',
      sortBy: 'estimated_loss_amount'
    },
    inventory: {
      stockStatus: '',
      needsReorder: false,
      category: '',
      sortBy: 'current_stock'
    },
    performance: {
      minScore: '',
      salesTrend: '',
      stockStatus: '',
      category: '',
      sortBy: 'performance_score'
    }
  });

  // Analytics pagination
  const [analyticsPagination, setAnalyticsPagination] = useState({
    unsold: { currentPage: 1, itemsPerPage: 10 },
    losses: { currentPage: 1, itemsPerPage: 10 },
    inventory: { currentPage: 1, itemsPerPage: 10 },
    performance: { currentPage: 1, itemsPerPage: 10 }
  });

  const [itemsPerPage, setItemsPerPage] = useState(filters.limit || 10);

  // Refs to track loading state and prevent duplicate requests
  const analyticsLoadingRef = useRef(false);
  const lastLoadedParamsRef = useRef<string>('');
  const loadTimeoutsRef = useRef<NodeJS.Timeout[]>([]);

  // Optimized analytics loading function
  const loadAnalyticsData = useCallback(async (period: number, category?: string, riskLevel?: string, searchTerm?: string) => {
    // Create a unique key for this request (include search term)
    const requestKey = `${period}-${category || 'all'}-${riskLevel || 'all'}-${searchTerm || 'all'}`;

    // Prevent duplicate requests
    if (analyticsLoadingRef.current || lastLoadedParamsRef.current === requestKey) {
      console.log('Analytics loading already in progress or same params, skipping...');
      return;
    }

    // Clear any existing timeouts
    loadTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    loadTimeoutsRef.current = [];

    analyticsLoadingRef.current = true;
    lastLoadedParamsRef.current = requestKey;

    try {
      console.log(`Loading analytics data: period=${period}, category=${category}, riskLevel=${riskLevel}`);

      // Load summary first for immediate feedback
      await fetchAnalyticsSummaryOnly(period);

      // Load other data with small delays to prevent overwhelming the server
      const timeouts: NodeJS.Timeout[] = [];

      timeouts.push(setTimeout(async () => {
        try {
          await fetchBestSellingProducts(period, 10, category);
        } catch (error) {
          console.error('Error loading best selling products:', error);
        }
      }, 100));

      timeouts.push(setTimeout(async () => {
        try {
          await fetchUnsoldProducts(period, category, searchTerm);
        } catch (error) {
          console.error('Error loading unsold products:', error);
        }
      }, 200));

      timeouts.push(setTimeout(async () => {
        try {
          await fetchExpectedLosses(period, riskLevel, searchTerm);
        } catch (error) {
          console.error('Error loading expected losses:', error);
        }
      }, 300));

      timeouts.push(setTimeout(async () => {
        try {
          await fetchInventoryStatus(undefined, category, searchTerm, period);
        } catch (error) {
          console.error('Error loading inventory status:', error);
        }
      }, 400));

      timeouts.push(setTimeout(async () => {
        try {
          await fetchPerformanceAnalysis(period, undefined, category, searchTerm);
        } catch (error) {
          console.error('Error loading performance analysis:', error);
        }
        // Mark loading as complete after the last request
        analyticsLoadingRef.current = false;
      }, 500));

      loadTimeoutsRef.current = timeouts;

    } catch (error) {
      console.error('Error loading analytics data:', error);
      analyticsLoadingRef.current = false;
    }
  }, [fetchAnalyticsSummaryOnly, fetchBestSellingProducts, fetchUnsoldProducts, fetchExpectedLosses, fetchInventoryStatus]);

  // حالات النوافذ
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    productId: number | null;
    productName: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    productId: null,
    productName: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
  }>({
    isOpen: false,
    title: '',
    message: ''
  });

  // Track if initial load has been done
  const initialLoadDone = useRef(false);

  useEffect(() => {
    // Prevent duplicate initial loads
    if (initialLoadDone.current) {
      return;
    }

    console.log('Component mounted, applying filters:', filters);
    console.log('Filter details on mount - search:', filters.search || 'none',
                'category:', filters.category || 'none',
                'lowStock:', filters.lowStock || false);

    // Check if we have state from navigation
    const locationState = location.state as { zeroStock?: boolean, search?: string } | null;

    if (locationState) {
      let newFilters = { ...filters };
      let filtersUpdated = false;

      if (locationState.zeroStock) {
        console.log('Applying zero stock filter from navigation state');
        setShowZeroStock(true);
        newFilters = {
          ...newFilters,
          zeroStock: true,
          page: 1,
          limit: 10
        };
        filtersUpdated = true;
      }

      if (locationState.search) {
        console.log('Applying search filter from navigation state:', locationState.search);
        setSearchTerm(locationState.search);
        newFilters = {
          ...newFilters,
          search: locationState.search,
          page: 1
        };
        filtersUpdated = true;
      }

      if (filtersUpdated) {
        setFilters(newFilters);
      } else {
        // Fetch products with current filters only once
        fetchProducts(filters);
      }
    } else {
      // Fetch products with current filters only once
      fetchProducts(filters);
    }

    // Fetch categories only once
    fetchCategories();

    // Show form when isNew is true
    if (isNew) {
      setSelectedProduct(null);
      setShowForm(true);
    }

    // Mark initial load as done
    initialLoadDone.current = true;

    return () => {};
  }, [isNew, location.state]);

  // Update local state when filters change
  useEffect(() => {
    console.log('Filters changed in store:', filters);
    setSearchTerm(filters.search || '');
    setSelectedCategory(filters.category || '');
    setShowLowStock(filters.lowStock || false);
    setShowZeroStock(filters.zeroStock || false);
    setIsActive(filters.isActive);
    setItemsPerPage(filters.limit || 10);
  }, [filters]);

  // Handle navigation from dashboard
  useEffect(() => {
    if (location.state?.activeTab) {
      setActiveTab(location.state.activeTab);
    }
    if (location.state?.analyticsTab) {
      setAnalyticsTab(location.state.analyticsTab);
    }
    if (location.state?.analyticsFilters) {
      setAnalyticsFilters(prev => ({
        ...prev,
        ...location.state.analyticsFilters
      }));
    }
  }, [location.state]);

  // Load analytics when tab changes with optimized loading (excluding search term for auto-reload)
  useEffect(() => {
    if (activeTab === 'analytics') {
      // جلب الفترات المتاحة أولاً
      fetchAvailablePeriods();

      // Only load if we haven't loaded yet or if parameters changed (excluding search term)
      const requestKey = `${selectedPeriod}-${analyticsCategory || 'all'}-${selectedRiskLevel || 'all'}`;
      if (lastLoadedParamsRef.current !== requestKey) {
        loadAnalyticsData(selectedPeriod, analyticsCategory || undefined, selectedRiskLevel || undefined, '');
      }
    } else {
      // Clear loading state when switching away from analytics
      analyticsLoadingRef.current = false;
      lastLoadedParamsRef.current = '';
      // Clear any pending timeouts
      loadTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      loadTimeoutsRef.current = [];
    }
  }, [activeTab, selectedPeriod, analyticsCategory, selectedRiskLevel, loadAnalyticsData, fetchAvailablePeriods]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      loadTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    };
  }, []);



  const handleDelete = (id: number, productName: string) => {
    setDeleteModal({
      isOpen: true,
      productId: id,
      productName: productName,
      isLoading: false
    });
  };

  const confirmDelete = async () => {
    if (!deleteModal.productId) return;

    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      await deleteProduct(deleteModal.productId);

      // إغلاق نافذة الحذف
      setDeleteModal({
        isOpen: false,
        productId: null,
        productName: '',
        isLoading: false
      });

      // عرض نافذة النجاح
      setSuccessModal({
        isOpen: true,
        title: 'تم الحذف بنجاح',
        message: `تم حذف المنتج "${deleteModal.productName}" بنجاح`
      });

      // إعادة تحميل المنتجات
      fetchProducts(filters);
    } catch (error: any) {
      // إغلاق نافذة الحذف
      setDeleteModal({
        isOpen: false,
        productId: null,
        productName: '',
        isLoading: false
      });

      // عرض رسالة الخطأ (ستظهر في error state من المتجر)
      console.error('Error deleting product:', error);
    }
  };

  // Analytics search function
  const handleAnalyticsSearch = (searchTerm: string) => {
    setAnalyticsSearchTerm(searchTerm);
    // Force refresh by clearing the cache
    analyticsLoadingRef.current = false;
    lastLoadedParamsRef.current = '';
    loadAnalyticsData(selectedPeriod, analyticsCategory || undefined, selectedRiskLevel || undefined, searchTerm);
  };

  // Analytics pagination functions
  const handleAnalyticsPageChange = (tab: string, page: number) => {
    setAnalyticsPagination(prev => ({
      ...prev,
      [tab]: { ...prev[tab as keyof typeof prev], currentPage: page }
    }));
  };

  const handleAnalyticsItemsPerPageChange = (tab: string, itemsPerPage: number) => {
    setAnalyticsPagination(prev => ({
      ...prev,
      [tab]: { currentPage: 1, itemsPerPage }
    }));
  };

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaBox className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المنتجات</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة وتتبع المخزون والمنتجات
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3 sm:gap-4 flex-wrap lg:flex-nowrap">
              <button
                onClick={() => fetchProducts()}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500 hover:shadow-md"
                title="تحديث"
              >
                <FaSync className={`text-sm ${loading ? "animate-spin" : ""}`} />
              </button>
              <button
                onClick={() => {
                  setSelectedProduct(null);
                  setShowForm(true);
                }}
                className="bg-primary-600 text-white rounded-xl py-3 px-4 sm:px-5 lg:px-6 hover:bg-primary-700 flex items-center transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl font-medium border-2 border-primary-600 hover:border-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-500/20"
              >
                <FaPlus className="ml-2 text-sm" />
                <span className="hidden sm:inline lg:inline">إضافة منتج جديد</span>
                <span className="sm:hidden lg:hidden">إضافة</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft mb-6 border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex">
            <button
              onClick={() => setActiveTab('products')}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                activeTab === 'products'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FaBox className="ml-2" />
              قائمة المنتجات
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                activeTab === 'analytics'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FaChartLine className="ml-2" />
              تحليلات المنتجات
            </button>
          </nav>
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'products' && (
        <>
          {/* Stats Bar */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
          {/* Total Products */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaBox className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">إجمالي المنتجات</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{products.length}</div>
            </div>
          </div>

          {/* Active Products */}
          <div className="flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30">
            <div className="bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaBox className="text-success-600 dark:text-success-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-success-700 dark:text-success-300 mb-1">المنتجات النشطة</div>
              <div className="text-xl font-bold text-success-600 dark:text-success-400">
                {products.filter(p => p.is_active).length}
              </div>
            </div>
          </div>

          {/* Low Stock Products */}
          <div className="flex items-center gap-3 p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg border border-warning-100 dark:border-warning-800/30">
            <div className="bg-warning-100 dark:bg-warning-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaExclamationTriangle className="text-warning-600 dark:text-warning-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-warning-700 dark:text-warning-300 mb-1">مخزون منخفض</div>
              <div className="text-xl font-bold text-warning-600 dark:text-warning-400">
                {products.filter(p => p.quantity <= p.min_quantity && p.quantity > 0).length}
              </div>
            </div>
          </div>

          {/* Out of Stock Products */}
          <div className="flex items-center gap-3 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-100 dark:border-danger-800/30">
            <div className="bg-danger-100 dark:bg-danger-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaExclamationTriangle className="text-danger-600 dark:text-danger-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-danger-700 dark:text-danger-300 mb-1">نفدت الكمية</div>
              <div className="text-xl font-bold text-danger-600 dark:text-danger-400">
                {products.filter(p => p.quantity === 0).length}
              </div>
            </div>
          </div>

          {/* Expected Total Profits */}
          <div className="flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30">
            <div className="bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaCoins className="text-success-600 dark:text-success-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-success-700 dark:text-success-300 mb-1">الأرباح المتوقعة</div>
              <div className="text-xl font-bold text-success-600 dark:text-success-400">
                <FormattedCurrency
                  amount={products
                    .filter(p => p.is_active && p.quantity > 0)
                    .reduce((total, product) => {
                      const profit = (product.price - product.cost_price) * product.quantity;
                      return total + profit;
                    }, 0)
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search & Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث عن منتج..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyUp={(e) => {
                  if (e.key === 'Enter') {
                    console.log('Applying search filter on Enter key:', searchTerm);
                    const newFilters = {
                      search: searchTerm,
                      category: selectedCategory,
                      lowStock: showLowStock
                    };
                    console.log('New filters to apply on Enter:', newFilters);
                    setFilters(newFilters);
                  }
                }}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>
            <button
              onClick={() => fetchProducts(filters)}
              className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
              title="تحديث"
            >
              <FaSync className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </button>
            <button
              onClick={() => {
                console.log('Applying search filter on button click:', searchTerm);
                const newFilters = {
                  search: searchTerm,
                  category: selectedCategory,
                  lowStock: showLowStock
                };
                setFilters(newFilters);
              }}
              className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
            >
              <FaSearch className="ml-2" />
              بحث
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4">
              {/* Category Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectInput
                  label="الفئة"
                  name="category"
                  value={selectedCategory}
                  onChange={(value: string) => {
                    setSelectedCategory(value);
                  }}
                  options={[
                    { value: '', label: 'جميع الفئات' },
                    ...categories.map(category => ({
                      value: category,
                      label: category
                    }))
                  ]}
                  placeholder="اختر الفئة..."
                />
              </div>

              {/* Items Per Page Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectInput
                  label="عدد العناصر"
                  name="itemsPerPage"
                  value={itemsPerPage.toString()}
                  onChange={(value: string) => {
                    const newValue = parseInt(value, 10);
                    setItemsPerPage(newValue);
                    setLimit(newValue);
                  }}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                  placeholder="اختر عدد العناصر..."
                />
              </div>

              {/* Low Stock Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  مخزون منخفض
                </label>
                <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                  <ToggleSwitch
                    id="lowStock"
                    checked={showLowStock}
                    onChange={(checked) => setShowLowStock(checked)}
                    label="منخفض فقط"
                    className="w-full"
                  />
                </div>
              </div>

              {/* Zero Stock Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  نفدت الكمية
                </label>
                <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                  <ToggleSwitch
                    id="zeroStock"
                    checked={showZeroStock}
                    onChange={(checked) => setShowZeroStock(checked)}
                    label="صفر فقط"
                    className="w-full"
                  />
                </div>
              </div>

              {/* Active Status Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectInput
                  label="حالة المنتج"
                  name="activeStatus"
                  value={isActive === true ? 'active' : isActive === false ? 'inactive' : ''}
                  onChange={(value: string) => {
                    if (value === 'active') {
                      setIsActive(true);
                    } else if (value === 'inactive') {
                      setIsActive(false);
                    } else {
                      setIsActive(undefined);
                    }
                  }}
                  options={[
                    { value: '', label: 'جميع المنتجات' },
                    { value: 'active', label: 'النشطة فقط' },
                    { value: 'inactive', label: 'غير النشطة فقط' }
                  ]}
                  placeholder="اختر حالة المنتج..."
                />
              </div>

              {/* Action Buttons */}
              <div className="sm:col-span-2 lg:col-span-1 flex flex-col gap-3">
                <button
                  type="button"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('');
                    setShowLowStock(false);
                    setShowZeroStock(false);
                    setIsActive(undefined);
                    setItemsPerPage(10);
                    const emptyFilters = { limit: 10 };
                    setFilters(emptyFilters);
                    console.log('Filters reset to empty');
                  }}
                  className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
                >
                  إعادة تعيين
                </button>
                <button
                  type="button"
                  onClick={() => {
                    console.log('Applying all filters');
                    const newFilters = {
                      search: searchTerm,
                      category: selectedCategory,
                      lowStock: showLowStock,
                      zeroStock: showZeroStock,
                      isActive: isActive,
                      limit: itemsPerPage,
                      page: 1
                    };
                    console.log('New filters to apply:', newFilters);
                    setFilters(newFilters);
                    console.log('Current pagination state:', pagination);
                  }}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20"
                >
                  تطبيق الفلاتر
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-danger-100 dark:bg-danger-900/30 border border-danger-200 dark:border-danger-800 text-danger-700 dark:text-danger-300 px-4 py-3 rounded-xl mb-6">
          {error}
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden">
        <div className="overflow-x-auto custom-scrollbar">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">
                  الترتيب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  اسم المنتج
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الفئة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  السعر
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  المخزون
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    جاري التحميل...
                  </td>
                </tr>
              ) : products.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    لم يتم العثور على منتجات
                  </td>
                </tr>
              ) : (
                products.map((product, index) => (
                  <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-sm font-semibold mx-auto">
                        {(pagination.page - 1) * pagination.limit + index + 1}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {product.barcode || 'بدون باركود'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {product.category || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-gray-100">
                        <FormattedCurrency amount={product.price} />
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        هامش الربح: {product.profit_margin.toFixed(1)}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div
                        className={`text-sm ${
                          product.quantity === 0
                            ? 'text-danger-600 dark:text-danger-400 font-medium'
                            : product.quantity <= product.min_quantity
                            ? 'text-warning-600 dark:text-warning-400 font-medium'
                            : 'text-gray-900 dark:text-gray-100'
                        }`}
                      >
                        {product.quantity} {product.unit}
                      </div>
                      {product.quantity === 0 ? (
                        <div className="text-xs text-danger-500 dark:text-danger-400 flex items-center">
                          <FaExclamationTriangle className="mr-1" /> نفدت الكمية
                        </div>
                      ) : product.quantity <= product.min_quantity ? (
                        <div className="text-xs text-warning-500 dark:text-warning-400 flex items-center">
                          <FaExclamationTriangle className="mr-1" /> مخزون منخفض
                        </div>
                      ) : null}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          product.is_active
                            ? 'bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300'
                            : 'bg-danger-100 dark:bg-danger-900/30 text-danger-800 dark:text-danger-300'
                        }`}
                      >
                        {product.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowForm(true);
                        }}
                        className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 ml-3"
                        title="تعديل"
                      >
                        <FaEdit className="inline text-lg" />
                      </button>
                      <button
                        onClick={() => handleDelete(product.id, product.name)}
                        className="text-danger-600 dark:text-danger-400 hover:text-danger-800 dark:hover:text-danger-300"
                        title="حذف"
                      >
                        <FaTrash className="inline text-lg" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && products.length > 0 && (
          <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div className="flex-1 flex justify-between items-center sm:hidden">
              <button
                onClick={() => {
                  console.log(`Moving to previous page from ${pagination.page}`);
                  prevPage();
                }}
                disabled={pagination.page <= 1}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page <= 1
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                السابق
              </button>

              <div className="text-xs text-gray-500 dark:text-gray-400">
                <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>
              </div>

              <button
                onClick={() => {
                  console.log(`Moving to next page from ${pagination.page}`);
                  nextPage();
                }}
                disabled={pagination.page >= pagination.pages}
                className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page >= pagination.pages
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                التالي
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>{' '}
                  <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {pagination.total})</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
                  <button
                    onClick={() => {
                      console.log(`Moving to previous page from ${pagination.page}`);
                      prevPage();
                    }}
                    disabled={pagination.page <= 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page <= 1
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">السابق</span>
                    <FaChevronRight className="h-5 w-5" />
                  </button>

                  {/* Page info - Only shown at the bottom of the pagination */}
                  <div className="hidden text-xs text-gray-500 dark:text-gray-400 px-2 py-1 border-r border-gray-300 dark:border-gray-600">
                    صفحة {pagination.page} من {pagination.pages}
                  </div>

                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (pagination.pages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      // If near start, show first 5
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.pages - 2) {
                      // If near end, show last 5
                      pageNum = pagination.pages - 4 + i;
                    } else {
                      // Otherwise show current and 2 on each side
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => {
                          console.log(`Setting page to ${pageNum}`);
                          setPage(pageNum);
                        }}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pagination.page === pageNum
                            ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300'
                            : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => {
                      console.log(`Moving to next page from ${pagination.page}`);
                      nextPage();
                    }}
                    disabled={pagination.page >= pagination.pages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page >= pagination.pages
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">التالي</span>
                    <FaChevronLeft className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
        </>
      )}

      {/* Analytics Tab Content */}
      {activeTab === 'analytics' && (
        <div className="space-y-6">
          {/* Analytics Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
                {/* Period Filter */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                      <FaCalendarAlt className="text-primary-600 dark:text-primary-400" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        فترة التحليل ({selectedPeriod} يوم)
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">تطبق على جميع التبويبات</p>
                    </div>
                  </div>

                  {/* Dynamic Period Selector */}
                  <div className="w-[420px] relative z-50">
                    <DynamicPeriodSelector
                      value={selectedPeriod}
                      onChange={(periodDays) => setPeriod(periodDays)}
                      className="min-w-[420px]"
                      disabled={analyticsInitialLoading}
                      label=""
                    />
                  </div>
                </div>

                {/* Search and Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center">
                  {/* Dynamic Search Field */}
                  <div className="w-full sm:w-80">
                    <DynamicSearchInput
                      value={analyticsSearchTerm}
                      onChange={setAnalyticsSearchTerm}
                      onSearch={handleAnalyticsSearch}
                      suggestions={getFilteredSuggestions(analyticsSearchTerm)}
                      placeholder="البحث بالاسم أو الباركود..."
                      disabled={analyticsInitialLoading}
                    />
                  </div>

                  <div className="flex gap-3">
                    <button
                      onClick={() => {
                        // Force refresh by clearing the cache
                        analyticsLoadingRef.current = false;
                        lastLoadedParamsRef.current = '';
                        loadAnalyticsData(selectedPeriod, analyticsCategory || undefined, selectedRiskLevel || undefined);
                      }}
                      className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl gap-2"
                      disabled={analyticsInitialLoading}
                    >
                      <FaSync className={`w-4 h-4 ${analyticsInitialLoading ? 'animate-spin' : ''}`} />
                      تحديث البيانات
                    </button>

                    <button
                      onClick={() => {
                        const exportData = {
                          summary: analytics?.summary,
                          best_selling: bestSellingProducts,
                          unsold_products: unsoldProducts,
                          expected_losses: expectedLosses,
                          inventory_status: inventoryStatus,
                          period_days: selectedPeriod,
                          generated_at: new Date().toISOString()
                        };

                        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `product-analytics-${selectedPeriod}days-${new Date().toISOString().split('T')[0]}.json`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                      }}
                      className="bg-success-600 hover:bg-success-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-success-600 hover:border-success-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-success-500/20 shadow-lg hover:shadow-xl gap-2"
                      disabled={!analytics}
                    >
                      <FaDownload className="w-4 h-4" />
                      تصدير
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Analytics Sub-tabs */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex overflow-x-auto">
                <button
                  onClick={() => setAnalyticsTab('overview')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ease-in-out ${
                    analyticsTab === 'overview'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                  }`}
                >
                  <FaChartLine className="ml-2" />
                  نظرة عامة
                </button>
                <button
                  onClick={() => setAnalyticsTab('bestselling')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ease-in-out ${
                    analyticsTab === 'bestselling'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                  }`}
                >
                  <FaTrophy className="ml-2" />
                  الأكثر مبيعاً
                </button>
                <button
                  onClick={() => setAnalyticsTab('unsold')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ease-in-out ${
                    analyticsTab === 'unsold'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                  }`}
                >
                  <FaExclamationCircle className="ml-2" />
                  غير المباعة
                </button>
                <button
                  onClick={() => setAnalyticsTab('losses')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ease-in-out ${
                    analyticsTab === 'losses'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                  }`}
                >
                  <FaExclamationTriangle className="ml-2" />
                  الخسائر المتوقعة
                </button>
                <button
                  onClick={() => setAnalyticsTab('inventory')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ease-in-out ${
                    analyticsTab === 'inventory'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                  }`}
                >
                  <FaWarehouse className="ml-2" />
                  حالة المخزون
                </button>
                <button
                  onClick={() => setAnalyticsTab('performance')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ease-in-out ${
                    analyticsTab === 'performance'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                  }`}
                >
                  <FaChartLine className="ml-2" />
                  تحليل الأداء
                </button>
              </nav>
            </div>
          </div>

          {/* Analytics Error */}
          {analyticsError && (
            <div className="bg-danger-100 dark:bg-danger-900/30 border border-danger-200 dark:border-danger-800 text-danger-700 dark:text-danger-300 px-4 py-3 rounded-xl">
              {analyticsError}
              <button
                onClick={clearAnalyticsError}
                className="mr-2 text-danger-600 hover:text-danger-800"
              >
                ×
              </button>
            </div>
          )}

          {/* Analytics Loading */}
          {analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-8 border border-gray-200 dark:border-gray-700">
              <div className="flex flex-col justify-center items-center space-y-4">
                <div className="relative">
                  <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-200 dark:border-primary-800"></div>
                  <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-600 border-t-transparent absolute top-0 left-0"></div>
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    جاري تحميل تحليلات المنتجات
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {analyticsLoadingSteps.summary ? 'تحميل الملخص...' :
                     analyticsLoadingSteps.bestSelling ? 'تحميل المنتجات الأكثر مبيعاً...' :
                     analyticsLoadingSteps.unsold ? 'تحميل المنتجات غير المباعة...' :
                     analyticsLoadingSteps.losses ? 'تحميل الخسائر المتوقعة...' :
                     analyticsLoadingSteps.inventory ? 'تحميل حالة المخزون...' :
                     analyticsLoadingSteps.performance ? 'تحميل تحليل الأداء...' :
                     'يتم تحليل البيانات وحساب الإحصائيات...'}
                  </p>
                  <div className="mt-4 bg-gray-100 dark:bg-gray-700 rounded-full h-2 w-64 mx-auto">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          (!analyticsLoadingSteps.summary ? 16.67 : 0) +
                          (!analyticsLoadingSteps.bestSelling ? 16.67 : 0) +
                          (!analyticsLoadingSteps.unsold ? 16.67 : 0) +
                          (!analyticsLoadingSteps.losses ? 16.67 : 0) +
                          (!analyticsLoadingSteps.inventory ? 16.67 : 0) +
                          (!analyticsLoadingSteps.performance ? 16.67 : 0)
                        }%`
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Analytics Content based on active sub-tab */}
          {analyticsTab === 'overview' && analytics && !analyticsInitialLoading && (
            <>
              {/* Header with Period Info */}
              <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-xl p-6 border border-primary-200 dark:border-primary-700 mb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-secondary-900 dark:text-secondary-100 mb-2">
                      تحليل شامل للمنتجات
                    </h3>
                    <p className="text-secondary-600 dark:text-secondary-400">
                      تحليل أداء المنتجات خلال فترة {analytics.period_days} يوم الماضية
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                      <FaCalendarAlt className="text-2xl text-primary-600 dark:text-primary-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">فترة التحليل</p>
                      <p className="text-lg font-bold text-primary-600 dark:text-primary-400">{analytics.period_days} يوم</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Main KPI Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">إجمالي المنتجات</p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">{analytics.summary.total_products}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">في النظام</p>
                    </div>
                    <div className="bg-primary-100 dark:bg-primary-900/30 p-3 rounded-full">
                      <FaBox className="text-2xl text-primary-600 dark:text-primary-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">منتجات مباعة</p>
                      <p className="text-3xl font-bold text-success-600 dark:text-success-400">{analytics.summary.products_with_sales}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        {((analytics.summary.products_with_sales / analytics.summary.total_products) * 100).toFixed(1)}% من الإجمالي
                      </p>
                    </div>
                    <div className="bg-success-100 dark:bg-success-900/30 p-3 rounded-full">
                      <FaTrophy className="text-2xl text-success-600 dark:text-success-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">منتجات غير مباعة</p>
                      <p className="text-3xl font-bold text-warning-600 dark:text-warning-400">{analytics.summary.products_without_sales}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        {((analytics.summary.products_without_sales / analytics.summary.total_products) * 100).toFixed(1)}% من الإجمالي
                      </p>
                    </div>
                    <div className="bg-warning-100 dark:bg-warning-900/30 p-3 rounded-full">
                      <FaExclamationCircle className="text-2xl text-warning-600 dark:text-warning-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">قيمة المخزون</p>
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        <FormattedCurrency amount={analytics.summary.total_stock_value} />
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">إجمالي القيمة</p>
                    </div>
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                      <FaWarehouse className="text-2xl text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Financial Analysis Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full ml-4">
                      <FaCoins className="text-2xl text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">التحليل المالي</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">تحليل الربحية والتكاليف</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">قيمة التكلفة الإجمالية:</span>
                      <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                        <FormattedCurrency amount={analytics.summary.total_cost_value} />
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">الربح المحتمل:</span>
                      <span className="text-lg font-bold text-green-600 dark:text-green-400">
                        <FormattedCurrency amount={analytics.summary.total_stock_value - analytics.summary.total_cost_value} />
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط هامش الربح:</span>
                      <span className="text-lg font-bold text-green-600 dark:text-green-400">{analytics.summary.average_profit_margin.toFixed(1)}%</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mt-4">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">نسبة الربح إلى التكلفة:</span>
                        <span className="text-sm font-bold text-green-600 dark:text-green-400">
                          {(((analytics.summary.total_stock_value - analytics.summary.total_cost_value) / analytics.summary.total_cost_value) * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full ml-4">
                      <FaChartLine className="text-2xl text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">أداء المبيعات</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">معدلات النجاح والأداء</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">معدل نجاح المنتجات:</span>
                      <span className="text-lg font-bold text-purple-600 dark:text-purple-400">
                        {((analytics.summary.products_with_sales / analytics.summary.total_products) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-purple-500 to-purple-600 h-3 rounded-full transition-all duration-500"
                        style={{ width: `${(analytics.summary.products_with_sales / analytics.summary.total_products) * 100}%` }}
                      ></div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <p className="text-xs font-medium text-gray-600 dark:text-gray-400">منتجات نشطة</p>
                        <p className="text-lg font-bold text-success-600 dark:text-success-400">{analytics.summary.products_with_sales}</p>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <p className="text-xs font-medium text-gray-600 dark:text-gray-400">تحتاج تحسين</p>
                        <p className="text-lg font-bold text-warning-600 dark:text-warning-400">{analytics.summary.products_without_sales}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions and Recommendations */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Quick Actions */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-full ml-4">
                      <FaExclamationTriangle className="text-xl text-orange-600 dark:text-orange-400" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">إجراءات سريعة</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">تحتاج انتباه فوري</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <button
                      onClick={() => setAnalyticsTab('unsold')}
                      className="w-full bg-orange-100 dark:bg-orange-800/50 hover:bg-orange-200 dark:hover:bg-orange-700/50 text-orange-800 dark:text-orange-200 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ease-in-out text-right border-2 border-orange-200 dark:border-orange-700 hover:border-orange-300 dark:hover:border-orange-600 focus:outline-none focus:ring-4 focus:ring-orange-500/20"
                    >
                      مراجعة المنتجات غير المباعة ({analytics.summary.products_without_sales})
                    </button>
                    {expectedLosses.length > 0 && (
                      <button
                        onClick={() => setAnalyticsTab('losses')}
                        className="w-full bg-red-100 dark:bg-red-800/50 hover:bg-red-200 dark:hover:bg-red-700/50 text-red-800 dark:text-red-200 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ease-in-out text-right border-2 border-red-200 dark:border-red-700 hover:border-red-300 dark:hover:border-red-600 focus:outline-none focus:ring-4 focus:ring-red-500/20"
                      >
                        مراجعة الخسائر المتوقعة ({expectedLosses.length})
                      </button>
                    )}
                    {inventoryStatus.filter(item => item.stock_status === 'low' || item.stock_status === 'out_of_stock').length > 0 && (
                      <button
                        onClick={() => setAnalyticsTab('inventory')}
                        className="w-full bg-yellow-100 dark:bg-yellow-800/50 hover:bg-yellow-200 dark:hover:bg-yellow-700/50 text-yellow-800 dark:text-yellow-200 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ease-in-out text-right border-2 border-yellow-200 dark:border-yellow-700 hover:border-yellow-300 dark:hover:border-yellow-600 focus:outline-none focus:ring-4 focus:ring-yellow-500/20"
                      >
                        مراجعة المخزون المنخفض ({inventoryStatus.filter(item => item.stock_status === 'low' || item.stock_status === 'out_of_stock').length})
                      </button>
                    )}
                  </div>
                </div>

                {/* Top Performers */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className="bg-emerald-100 dark:bg-emerald-900/30 p-3 rounded-full ml-4">
                      <FaTrophy className="text-xl text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">أفضل المنتجات</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">الأداء المتميز</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    {bestSellingProducts.slice(0, 3).map((product, index) => (
                      <div key={product.id} className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold text-white mr-2 ${
                              index === 0 ? 'bg-yellow-500' :
                              index === 1 ? 'bg-gray-400' :
                              'bg-orange-600'
                            }`}>
                              {index + 1}
                            </span>
                            <span className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
                              {product.name}
                            </span>
                          </div>
                          <span className="text-xs font-bold text-emerald-600 dark:text-emerald-400">
                            {product.total_sold}
                          </span>
                        </div>
                      </div>
                    ))}
                    <button
                      onClick={() => setAnalyticsTab('bestselling')}
                      className="w-full bg-emerald-100 dark:bg-emerald-900/30 hover:bg-emerald-200 dark:hover:bg-emerald-800/50 text-emerald-700 dark:text-emerald-300 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ease-in-out border-2 border-emerald-200 dark:border-emerald-700 hover:border-emerald-300 dark:hover:border-emerald-600 focus:outline-none focus:ring-4 focus:ring-emerald-500/20"
                    >
                      عرض جميع المنتجات المتميزة
                    </button>
                  </div>
                </div>

                {/* Key Insights */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full ml-4">
                      <FaChartLine className="text-xl text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">رؤى مهمة</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">تحليل ذكي</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                      <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">معدل دوران المخزون</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {analytics.summary.products_with_sales > 0 ?
                          `${((analytics.summary.products_with_sales / analytics.summary.total_products) * 100).toFixed(0)}% من المنتجات تحقق مبيعات` :
                          'لا توجد مبيعات في الفترة المحددة'
                        }
                      </p>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                      <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">كفاءة رأس المال</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {analytics.summary.total_cost_value > 0 ?
                          `عائد ${(((analytics.summary.total_stock_value - analytics.summary.total_cost_value) / analytics.summary.total_cost_value) * 100).toFixed(1)}% على الاستثمار` :
                          'لا توجد بيانات تكلفة'
                        }
                      </p>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                      <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">توصية</p>
                      <p className="text-xs text-blue-600 dark:text-blue-400">
                        {analytics.summary.products_without_sales > analytics.summary.products_with_sales ?
                          'ركز على تحسين المنتجات غير المباعة' :
                          'استمر في الاستثمار في المنتجات الناجحة'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Best Selling Products Tab */}
          {analyticsTab === 'bestselling' && bestSellingProducts.length > 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
              <div className="p-4 sm:p-6">
                <div className="flex flex-col space-y-4">
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                        <FaTrophy className="ml-2 text-success-600 dark:text-success-400" />
                        المنتجات الأكثر مبيعاً
                        <span className="mr-2 bg-success-100 dark:bg-success-900/30 text-success-600 dark:text-success-400 px-2 py-1 rounded-full text-xs font-medium">
                          {bestSellingProducts.length} منتج
                        </span>
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        قائمة بأفضل المنتجات أداءً خلال فترة {selectedPeriod} يوم
                      </p>
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 items-end">
                      {/* Min Sales Filter */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          أقل مبيعات
                        </label>
                        <div className="bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center px-4 transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 focus-within:border-primary-500 focus-within:ring-4 focus-within:ring-primary-500/20">
                          <input
                            type="number"
                            name="minSales"
                            value={analyticsFilters.bestselling.minSales || ''}
                            onChange={(e) => setAnalyticsFilters(prev => ({
                              ...prev,
                              bestselling: { ...prev.bestselling, minSales: e.target.value }
                            }))}
                            placeholder="0"
                            className="w-full bg-transparent border-none outline-none text-sm text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                      </div>

                      {/* Min Revenue Filter */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          أقل إيرادات
                        </label>
                        <div className="bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center px-4 transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 focus-within:border-primary-500 focus-within:ring-4 focus-within:ring-primary-500/20">
                          <input
                            type="number"
                            name="minRevenue"
                            value={analyticsFilters.bestselling.minRevenue || ''}
                            onChange={(e) => setAnalyticsFilters(prev => ({
                              ...prev,
                              bestselling: { ...prev.bestselling, minRevenue: e.target.value }
                            }))}
                            placeholder="0"
                            className="w-full bg-transparent border-none outline-none text-sm text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                      </div>

                      {/* Category Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="الفئة"
                          name="category"
                          value={analyticsFilters.bestselling.category}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            bestselling: { ...prev.bestselling, category: value }
                          }))}
                          options={[
                            { value: '', label: 'جميع الفئات' },
                            ...categories.map(category => ({
                              value: category,
                              label: category
                            }))
                          ]}
                          placeholder="اختر الفئة..."
                        />
                      </div>

                      {/* Sort By Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="ترتيب حسب"
                          name="sortBy"
                          value={analyticsFilters.bestselling.sortBy}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            bestselling: { ...prev.bestselling, sortBy: value }
                          }))}
                          options={[
                            { value: 'total_sold', label: 'الكمية المباعة' },
                            { value: 'total_revenue', label: 'الإيرادات' },
                            { value: 'total_profit', label: 'الأرباح' },
                            { value: 'profit_margin', label: 'هامش الربح' }
                          ]}
                          placeholder="اختر طريقة الترتيب..."
                        />
                      </div>

                      {/* Reset Button */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-transparent">
                          إعادة تعيين
                        </label>
                        <button
                          onClick={() => {
                            setAnalyticsFilters(prev => ({
                              ...prev,
                              bestselling: {
                                minSales: '',
                                minRevenue: '',
                                category: '',
                                sortBy: 'total_sold'
                              }
                            }));
                          }}
                          className="w-full bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px] h-12"
                        >
                          <FaSync className="ml-1 w-3 h-3" />
                          إعادة تعيين
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700"></div>

              <div className="overflow-x-auto rounded-b-xl">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الترتيب</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">اسم المنتج</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الفئة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الكمية المباعة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">إجمالي الإيرادات</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">إجمالي الأرباح</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">هامش الربح</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">نسبة المبيعات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {getFilteredBestSelling().length > 0 ? (
                      getFilteredBestSelling().map((product, index) => {
                        // حساب الترتيب الثابت بناءً على النتائج المفلترة
                        const rank = index + 1;

                        return (
                          <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center gap-3">
                                {/* الرقم مع الخلفية الملونة */}
                                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold text-white shadow-sm ${
                                  rank === 1 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' :
                                  rank === 2 ? 'bg-gradient-to-r from-gray-300 to-gray-500' :
                                  rank === 3 ? 'bg-gradient-to-r from-orange-400 to-orange-600' :
                                  rank <= 5 ? 'bg-gradient-to-r from-blue-400 to-blue-600' :
                                  rank <= 10 ? 'bg-gradient-to-r from-green-400 to-green-600' :
                                  'bg-gradient-to-r from-gray-400 to-gray-600'
                                }`}>
                                  {rank}
                                </div>

                                {/* الأيقونة المناسبة */}
                                <div className="flex items-center">
                                  {rank === 1 && (
                                    <div className="flex items-center gap-1">
                                      <FaCrown className="text-yellow-500 text-lg" />
                                      <FaTrophy className="text-yellow-600 text-sm" />
                                    </div>
                                  )}
                                  {rank === 2 && (
                                    <FaMedal className="text-gray-500 text-lg" />
                                  )}
                                  {rank === 3 && (
                                    <FaMedal className="text-orange-600 text-lg" />
                                  )}
                                  {rank <= 5 && rank > 3 && (
                                    <FaStar className="text-blue-500 text-lg" />
                                  )}
                                  {rank <= 10 && rank > 5 && (
                                    <FaFire className="text-green-500 text-lg" />
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                              <div>
                                <div className="font-medium">{product.name}</div>
                                {product.barcode && (
                                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    {product.barcode}
                                  </div>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {product.category || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {product.total_sold.toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              <FormattedCurrency amount={product.total_revenue} />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-success-600 dark:text-success-400 font-medium">
                              <FormattedCurrency amount={product.total_profit} />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {product.profit_margin.toFixed(1)}%
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {product.percentage_of_total_sales.toFixed(1)}%
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={8} className="px-6 py-12 text-center">
                          <FaTrophy className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            لا توجد نتائج مطابقة للفلاتر
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            جرب تعديل الفلاتر للحصول على نتائج مختلفة
                          </p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Unsold Products Tab */}
          {analyticsTab === 'unsold' && unsoldProducts.length > 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
              <div className="p-4 sm:p-6">
                <div className="flex flex-col space-y-4">
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                        <FaExclamationCircle className="ml-2 text-warning-600 dark:text-warning-400" />
                        المنتجات غير المباعة
                        <span className="mr-2 bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-400 px-2 py-1 rounded-full text-xs font-medium">
                          {unsoldProducts.length} منتج
                        </span>
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        المنتجات التي لم تحقق أي مبيعات خلال فترة {selectedPeriod} يوم
                      </p>
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 items-end">
                      {/* Min Days Filter */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          أقل أيام
                        </label>
                        <div className="bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center px-4 transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 focus-within:border-primary-500 focus-within:ring-4 focus-within:ring-primary-500/20">
                          <input
                            type="number"
                            name="minDaysInStock"
                            value={analyticsFilters.unsold.minDaysInStock || ''}
                            onChange={(e) => setAnalyticsFilters(prev => ({
                              ...prev,
                              unsold: { ...prev.unsold, minDaysInStock: e.target.value }
                            }))}
                            placeholder="0"
                            className="w-full bg-transparent border-none outline-none text-sm text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                      </div>

                      {/* Max Stock Value Filter */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          أقصى قيمة
                        </label>
                        <div className="bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center px-4 transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 focus-within:border-primary-500 focus-within:ring-4 focus-within:ring-primary-500/20">
                          <input
                            type="number"
                            name="maxStockValue"
                            value={analyticsFilters.unsold.maxStockValue || ''}
                            onChange={(e) => setAnalyticsFilters(prev => ({
                              ...prev,
                              unsold: { ...prev.unsold, maxStockValue: e.target.value }
                            }))}
                            placeholder="0"
                            className="w-full bg-transparent border-none outline-none text-sm text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                      </div>

                      {/* Category Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="الفئة"
                          name="category"
                          value={analyticsFilters.unsold.category}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            unsold: { ...prev.unsold, category: value }
                          }))}
                          options={[
                            { value: '', label: 'جميع الفئات' },
                            ...categories.map(category => ({
                              value: category,
                              label: category
                            }))
                          ]}
                          placeholder="اختر الفئة..."
                        />
                      </div>

                      {/* Sort By Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="ترتيب حسب"
                          name="sortBy"
                          value={analyticsFilters.unsold.sortBy}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            unsold: { ...prev.unsold, sortBy: value }
                          }))}
                          options={[
                            { value: 'days_in_stock', label: 'أيام في المخزون' },
                            { value: 'stock_value', label: 'قيمة المخزون' },
                            { value: 'potential_loss', label: 'الخسارة المحتملة' },
                            { value: 'current_stock', label: 'المخزون الحالي' }
                          ]}
                          placeholder="اختر طريقة الترتيب..."
                        />
                      </div>

                      {/* Items per page selector */}
                      <div className="space-y-2">
                        <SelectInput
                          label="عدد السجلات"
                          name="itemsPerPage"
                          value={analyticsPagination.unsold.itemsPerPage.toString()}
                          onChange={(value: string) => handleAnalyticsItemsPerPageChange('unsold', parseInt(value))}
                          options={[
                            { value: '10', label: '10 سجل' },
                            { value: '20', label: '20 سجل' },
                            { value: '30', label: '30 سجل' },
                            { value: '50', label: '50 سجل' }
                          ]}
                          placeholder="اختر عدد السجلات..."
                        />
                      </div>

                      {/* Reset Button */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-transparent">
                          إعادة تعيين
                        </label>
                        <button
                          onClick={() => {
                            setAnalyticsFilters(prev => ({
                              ...prev,
                              unsold: {
                                minDaysInStock: '',
                                maxStockValue: '',
                                category: '',
                                sortBy: 'days_in_stock'
                              }
                            }));
                            setAnalyticsPagination(prev => ({
                              ...prev,
                              unsold: { currentPage: 1, itemsPerPage: 10 }
                            }));
                          }}
                          className="w-full bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px] h-12"
                        >
                          <FaSync className="ml-1 w-3 h-3" />
                          إعادة تعيين
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700"></div>
              <div className="overflow-x-auto rounded-b-xl">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase w-16">الترتيب</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">اسم المنتج</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الفئة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">المخزون الحالي</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">قيمة المخزون</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">قيمة التكلفة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">أيام في المخزون</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الخسارة المحتملة</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {getFilteredUnsold().data.length > 0 ? (
                      getFilteredUnsold().data.map((product, index) => {
                        // حساب الترتيب الثابت بناءً على الصفحة الحالية والفهرس
                        const rank = (analyticsPagination.unsold.currentPage - 1) * analyticsPagination.unsold.itemsPerPage + index + 1;

                        return (
                          <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <div className="w-8 h-8 bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-400 rounded-full flex items-center justify-center text-sm font-semibold mx-auto">
                                {rank}
                              </div>
                            </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                            <div>
                              <div className="font-medium">{product.name}</div>
                              {product.barcode && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  {product.barcode}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {product.category || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {product.current_stock.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <FormattedCurrency amount={product.stock_value} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <FormattedCurrency amount={product.cost_value} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {product.days_in_stock}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-danger-600 dark:text-danger-400 font-medium">
                            <FormattedCurrency amount={product.potential_loss} />
                          </td>
                        </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={8} className="px-6 py-12 text-center">
                          <FaExclamationCircle className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            لا توجد نتائج مطابقة للفلاتر
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            جرب تعديل الفلاتر للحصول على نتائج مختلفة
                          </p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <Pagination
                currentPage={analyticsPagination.unsold.currentPage}
                totalPages={getFilteredUnsold().totalPages}
                itemsPerPage={analyticsPagination.unsold.itemsPerPage}
                totalItems={getFilteredUnsold().total}
                onPageChange={(page) => handleAnalyticsPageChange('unsold', page)}
                onItemsPerPageChange={(itemsPerPage) => handleAnalyticsItemsPerPageChange('unsold', itemsPerPage)}
              />
            </div>
          )}

          {/* Expected Losses Tab */}
          {analyticsTab === 'losses' && expectedLosses.length > 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
              <div className="p-4 sm:p-6">
                <div className="flex flex-col space-y-4">
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                        <FaExclamationTriangle className="ml-2 text-danger-600 dark:text-danger-400" />
                        الخسائر المتوقعة
                        <span className="mr-2 bg-danger-100 dark:bg-danger-900/30 text-danger-600 dark:text-danger-400 px-2 py-1 rounded-full text-xs font-medium">
                          {expectedLosses.length} منتج
                        </span>
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        المنتجات المعرضة لخطر الخسارة بناءً على ضعف المبيعات (فترة التحليل: {selectedPeriod} يوم)
                      </p>
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 items-end">
                      {/* Risk Level Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="مستوى المخاطر"
                          name="riskLevel"
                          value={analyticsFilters.losses.riskLevel}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            losses: { ...prev.losses, riskLevel: value }
                          }))}
                          options={[
                            { value: '', label: 'كل المخاطر' },
                            { value: 'high_risk', label: 'مخاطر عالية' },
                            { value: 'medium_risk', label: 'مخاطر متوسطة' },
                            { value: 'low_risk', label: 'مخاطر منخفضة' }
                          ]}
                          placeholder="اختر مستوى المخاطر..."
                        />
                      </div>

                      {/* Min Loss Amount Filter */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          أقل خسارة
                        </label>
                        <div className="bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center px-4 transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 focus-within:border-primary-500 focus-within:ring-4 focus-within:ring-primary-500/20">
                          <input
                            type="number"
                            name="minLossAmount"
                            value={analyticsFilters.losses.minLossAmount || ''}
                            onChange={(e) => setAnalyticsFilters(prev => ({
                              ...prev,
                              losses: { ...prev.losses, minLossAmount: e.target.value }
                            }))}
                            placeholder="0"
                            className="w-full bg-transparent border-none outline-none text-sm text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                      </div>

                      {/* Category Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="الفئة"
                          name="category"
                          value={analyticsFilters.losses.category}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            losses: { ...prev.losses, category: value }
                          }))}
                          options={[
                            { value: '', label: 'جميع الفئات' },
                            ...categories.map(category => ({
                              value: category,
                              label: category
                            }))
                          ]}
                          placeholder="اختر الفئة..."
                        />
                      </div>

                      {/* Sort By Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="ترتيب حسب"
                          name="sortBy"
                          value={analyticsFilters.losses.sortBy}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            losses: { ...prev.losses, sortBy: value }
                          }))}
                          options={[
                            { value: 'estimated_loss_amount', label: 'مقدار الخسارة' },
                            { value: 'days_without_sales', label: 'أيام بدون مبيعات' },
                            { value: 'current_stock', label: 'المخزون الحالي' }
                          ]}
                          placeholder="اختر طريقة الترتيب..."
                        />
                      </div>

                      {/* Items per page selector */}
                      <div className="space-y-2">
                        <SelectInput
                          label="عدد السجلات"
                          name="itemsPerPage"
                          value={analyticsPagination.losses.itemsPerPage.toString()}
                          onChange={(value: string) => handleAnalyticsItemsPerPageChange('losses', parseInt(value))}
                          options={[
                            { value: '10', label: '10 سجل' },
                            { value: '20', label: '20 سجل' },
                            { value: '30', label: '30 سجل' },
                            { value: '50', label: '50 سجل' }
                          ]}
                          placeholder="اختر عدد السجلات..."
                        />
                      </div>

                      {/* Reset Button */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-transparent">
                          إعادة تعيين
                        </label>
                        <button
                          onClick={() => {
                            setAnalyticsFilters(prev => ({
                              ...prev,
                              losses: {
                                riskLevel: '',
                                minLossAmount: '',
                                category: '',
                                sortBy: 'estimated_loss_amount'
                              }
                            }));
                            setAnalyticsPagination(prev => ({
                              ...prev,
                              losses: { currentPage: 1, itemsPerPage: 10 }
                            }));
                          }}
                          className="w-full bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px] h-12"
                        >
                          <FaSync className="ml-1 w-3 h-3" />
                          إعادة تعيين
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700"></div>
              <div className="overflow-x-auto rounded-b-xl">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase w-16">الترتيب</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">اسم المنتج</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الفئة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">المخزون</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">أيام بدون مبيعات</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">مقدار الخسارة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">مستوى المخاطر</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">التوصية</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {getFilteredLosses().data.length > 0 ? (
                      getFilteredLosses().data.map((loss, index) => {
                        // حساب الترتيب الثابت بناءً على الصفحة الحالية والفهرس
                        const rank = (analyticsPagination.losses.currentPage - 1) * analyticsPagination.losses.itemsPerPage + index + 1;

                        return (
                          <tr key={loss.product_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <div className="w-8 h-8 bg-danger-100 dark:bg-danger-900/30 text-danger-600 dark:text-danger-400 rounded-full flex items-center justify-center text-sm font-semibold mx-auto">
                                {rank}
                              </div>
                            </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                            <div>
                              <div className="font-medium">{loss.product_name}</div>
                              {loss.product_barcode && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  {loss.product_barcode}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {loss.category || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {loss.current_stock.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {loss.days_without_sales}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-danger-600 dark:text-danger-400 font-medium">
                            <FormattedCurrency amount={loss.estimated_loss_amount} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                              loss.loss_category === 'high_risk' ? 'bg-danger-100 text-danger-800 dark:bg-danger-900/30 dark:text-danger-300' :
                              loss.loss_category === 'medium_risk' ? 'bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300' :
                              'bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300'
                            }`}>
                              {loss.loss_category === 'high_risk' ? 'مخاطر عالية' :
                               loss.loss_category === 'medium_risk' ? 'مخاطر متوسطة' : 'مخاطر منخفضة'}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                            {loss.recommendation}
                          </td>
                        </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={8} className="px-6 py-12 text-center">
                          <FaExclamationTriangle className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            لا توجد نتائج مطابقة للفلاتر
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            جرب تعديل الفلاتر للحصول على نتائج مختلفة
                          </p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <Pagination
                currentPage={analyticsPagination.losses.currentPage}
                totalPages={getFilteredLosses().totalPages}
                itemsPerPage={analyticsPagination.losses.itemsPerPage}
                totalItems={getFilteredLosses().total}
                onPageChange={(page) => handleAnalyticsPageChange('losses', page)}
                onItemsPerPageChange={(itemsPerPage) => handleAnalyticsItemsPerPageChange('losses', itemsPerPage)}
              />
            </div>
          )}

          {/* Inventory Status Tab */}
          {analyticsTab === 'inventory' && inventoryStatus.length > 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
              <div className="p-4 sm:p-6">
                <div className="flex flex-col space-y-4">
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                        <FaWarehouse className="ml-2 text-blue-600 dark:text-blue-400" />
                        حالة المخزون
                        <span className="mr-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full text-xs font-medium">
                          {inventoryStatus.length} منتج
                        </span>
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        تحليل مستويات المخزون وتوصيات إعادة الطلب (بناءً على مبيعات {selectedPeriod} يوم)
                      </p>
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 items-end">
                      {/* Stock Status Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="حالة المخزون"
                          name="stockStatus"
                          value={analyticsFilters.inventory.stockStatus}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            inventory: { ...prev.inventory, stockStatus: value }
                          }))}
                          options={[
                            { value: '', label: 'كل الحالات' },
                            { value: 'out_of_stock', label: 'نفد المخزون' },
                            { value: 'low', label: 'منخفض' },
                            { value: 'healthy', label: 'صحي' },
                            { value: 'overstocked', label: 'مفرط' }
                          ]}
                          placeholder="اختر حالة المخزون..."
                        />
                      </div>

                      {/* Needs Reorder Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="يحتاج طلب"
                          name="needsReorder"
                          value={analyticsFilters.inventory.needsReorder ? 'true' : 'false'}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            inventory: { ...prev.inventory, needsReorder: value === 'true' }
                          }))}
                          options={[
                            { value: 'false', label: 'الكل' },
                            { value: 'true', label: 'يحتاج طلب فقط' }
                          ]}
                          placeholder="اختر حالة الطلب..."
                        />
                      </div>

                      {/* Category Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="الفئة"
                          name="category"
                          value={analyticsFilters.inventory.category}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            inventory: { ...prev.inventory, category: value }
                          }))}
                          options={[
                            { value: '', label: 'جميع الفئات' },
                            ...categories.map(category => ({
                              value: category,
                              label: category
                            }))
                          ]}
                          placeholder="اختر الفئة..."
                        />
                      </div>

                      {/* Sort By Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="ترتيب حسب"
                          name="sortBy"
                          value={analyticsFilters.inventory.sortBy}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            inventory: { ...prev.inventory, sortBy: value }
                          }))}
                          options={[
                            { value: 'current_stock', label: 'المخزون الحالي' },
                            { value: 'days_of_supply', label: 'أيام التوريد' },
                            { value: 'suggested_order_quantity', label: 'الكمية المقترحة' },
                            { value: 'reorder_point', label: 'نقطة إعادة الطلب' }
                          ]}
                          placeholder="اختر طريقة الترتيب..."
                        />
                      </div>

                      {/* Items per page selector */}
                      <div className="space-y-2">
                        <SelectInput
                          label="عدد السجلات"
                          name="itemsPerPage"
                          value={analyticsPagination.inventory.itemsPerPage.toString()}
                          onChange={(value: string) => handleAnalyticsItemsPerPageChange('inventory', parseInt(value))}
                          options={[
                            { value: '10', label: '10 سجل' },
                            { value: '20', label: '20 سجل' },
                            { value: '30', label: '30 سجل' },
                            { value: '50', label: '50 سجل' }
                          ]}
                          placeholder="اختر عدد السجلات..."
                        />
                      </div>

                      {/* Reset Button */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-transparent">
                          إعادة تعيين
                        </label>
                        <button
                          onClick={() => {
                            setAnalyticsFilters(prev => ({
                              ...prev,
                              inventory: {
                                stockStatus: '',
                                needsReorder: false,
                                category: '',
                                sortBy: 'current_stock'
                              }
                            }));
                            setAnalyticsPagination(prev => ({
                              ...prev,
                              inventory: { currentPage: 1, itemsPerPage: 10 }
                            }));
                          }}
                          className="w-full bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px] h-12"
                        >
                          <FaSync className="ml-1 w-3 h-3" />
                          إعادة تعيين
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700"></div>
              <div className="overflow-x-auto rounded-b-xl">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase w-16">الترتيب</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">اسم المنتج</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الفئة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">المخزون الحالي</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الحد الأدنى</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">أيام التوريد</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">حالة المخزون</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">نقطة إعادة الطلب</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الكمية المقترحة</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {getFilteredInventory().data.length > 0 ? (
                      getFilteredInventory().data.map((item, index) => {
                        // حساب الترتيب الثابت بناءً على الصفحة الحالية والفهرس
                        const rank = (analyticsPagination.inventory.currentPage - 1) * analyticsPagination.inventory.itemsPerPage + index + 1;

                        return (
                          <tr key={item.product_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-sm font-semibold mx-auto">
                                {rank}
                              </div>
                            </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                            <div>
                              <div className="font-medium">{item.product_name}</div>
                              {item.product_barcode && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  {item.product_barcode}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {item.category || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {item.current_stock.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {item.min_quantity.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {item.days_of_supply === 999 ? '∞' :
                             item.days_of_supply < 0.25 ? 'أقل من ربع يوم' :
                             item.days_of_supply < 0.5 ? 'ربع يوم' :
                             item.days_of_supply < 1 ? 'نصف يوم' :
                             item.days_of_supply < 1.5 ? 'يوم واحد' :
                             item.days_of_supply < 2 ? 'يوم ونصف' :
                             item.days_of_supply < 7 ? `${Math.round(item.days_of_supply)} أيام` :
                             item.days_of_supply < 30 ? `${Math.round(item.days_of_supply)} يوم` :
                             item.days_of_supply < 90 ? `${Math.round(item.days_of_supply / 7)} أسابيع` :
                             `${Math.round(item.days_of_supply / 30)} شهور`}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                              item.stock_status === 'out_of_stock' ? 'bg-danger-100 text-danger-800 dark:bg-danger-900/30 dark:text-danger-300' :
                              item.stock_status === 'low' ? 'bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300' :
                              item.stock_status === 'overstocked' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                              'bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300'
                            }`}>
                              {item.stock_status === 'out_of_stock' ? 'نفد المخزون' :
                               item.stock_status === 'low' ? 'منخفض' :
                               item.stock_status === 'overstocked' ? 'مفرط' : 'صحي'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {item.reorder_point.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {item.suggested_order_quantity > 0 ? (
                              <span className="text-primary-600 dark:text-primary-400">
                                {item.suggested_order_quantity.toLocaleString()}
                              </span>
                            ) : (
                              <span className="text-gray-500 dark:text-gray-400">-</span>
                            )}
                          </td>
                        </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={9} className="px-6 py-12 text-center">
                          <FaWarehouse className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            لا توجد نتائج مطابقة للفلاتر
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            جرب تعديل الفلاتر للحصول على نتائج مختلفة
                          </p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <Pagination
                currentPage={analyticsPagination.inventory.currentPage}
                totalPages={getFilteredInventory().totalPages}
                itemsPerPage={analyticsPagination.inventory.itemsPerPage}
                totalItems={getFilteredInventory().total}
                onPageChange={(page) => handleAnalyticsPageChange('inventory', page)}
                onItemsPerPageChange={(itemsPerPage) => handleAnalyticsItemsPerPageChange('inventory', itemsPerPage)}
              />
            </div>
          )}

          {/* Performance Analysis Tab */}
          {analyticsTab === 'performance' && performanceAnalysis.length > 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
              <div className="p-4 sm:p-6">
                <div className="flex flex-col space-y-4">
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                        <FaChartLine className="ml-2 text-purple-600 dark:text-purple-400" />
                        تحليل الأداء الشامل
                        <span className="mr-2 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 px-2 py-1 rounded-full text-xs font-medium">
                          {performanceAnalysis.length} منتج
                        </span>
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        تحليل شامل لأداء المنتجات مع التوصيات (فترة التحليل: {selectedPeriod} يوم)
                      </p>
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 items-end">
                      {/* Min Score Filter */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          أقل نقاط
                        </label>
                        <div className="bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center px-4 transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 focus-within:border-primary-500 focus-within:ring-4 focus-within:ring-primary-500/20">
                          <input
                            type="number"
                            name="minScore"
                            value={analyticsFilters.performance.minScore || ''}
                            onChange={(e) => setAnalyticsFilters(prev => ({
                              ...prev,
                              performance: { ...prev.performance, minScore: e.target.value }
                            }))}
                            placeholder="0"
                            className="w-full bg-transparent border-none outline-none text-sm text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                      </div>

                      {/* Sales Trend Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="اتجاه المبيعات"
                          name="salesTrend"
                          value={analyticsFilters.performance.salesTrend}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            performance: { ...prev.performance, salesTrend: value }
                          }))}
                          options={[
                            { value: '', label: 'كل الاتجاهات' },
                            { value: 'increasing', label: 'متزايد' },
                            { value: 'stable', label: 'مستقر' },
                            { value: 'decreasing', label: 'متناقص' },
                            { value: 'no_sales', label: 'لا مبيعات' }
                          ]}
                          placeholder="اختر اتجاه المبيعات..."
                        />
                      </div>

                      {/* Category Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="الفئة"
                          name="category"
                          value={analyticsFilters.performance.category}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            performance: { ...prev.performance, category: value }
                          }))}
                          options={[
                            { value: '', label: 'جميع الفئات' },
                            ...categories.map(category => ({
                              value: category,
                              label: category
                            }))
                          ]}
                          placeholder="اختر الفئة..."
                        />
                      </div>

                      {/* Sort By Filter */}
                      <div className="space-y-2">
                        <SelectInput
                          label="ترتيب حسب"
                          name="sortBy"
                          value={analyticsFilters.performance.sortBy}
                          onChange={(value: string) => setAnalyticsFilters(prev => ({
                            ...prev,
                            performance: { ...prev.performance, sortBy: value }
                          }))}
                          options={[
                            { value: 'performance_score', label: 'نقاط الأداء' },
                            { value: 'total_sold', label: 'الكمية المباعة' },
                            { value: 'total_revenue', label: 'الإيرادات' },
                            { value: 'total_profit', label: 'الأرباح' }
                          ]}
                          placeholder="اختر طريقة الترتيب..."
                        />
                      </div>

                      {/* Items per page selector */}
                      <div className="space-y-2">
                        <SelectInput
                          label="عدد السجلات"
                          name="itemsPerPage"
                          value={analyticsPagination.performance.itemsPerPage.toString()}
                          onChange={(value: string) => handleAnalyticsItemsPerPageChange('performance', parseInt(value))}
                          options={[
                            { value: '10', label: '10 سجل' },
                            { value: '20', label: '20 سجل' },
                            { value: '30', label: '30 سجل' },
                            { value: '50', label: '50 سجل' }
                          ]}
                          placeholder="اختر عدد السجلات..."
                        />
                      </div>

                      {/* Reset Button */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-transparent">
                          إعادة تعيين
                        </label>
                        <button
                          onClick={() => {
                            setAnalyticsFilters(prev => ({
                              ...prev,
                              performance: {
                                minScore: '',
                                salesTrend: '',
                                stockStatus: '',
                                category: '',
                                sortBy: 'performance_score'
                              }
                            }));
                            setAnalyticsPagination(prev => ({
                              ...prev,
                              performance: { currentPage: 1, itemsPerPage: 10 }
                            }));
                          }}
                          className="w-full bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px] h-12"
                        >
                          <FaSync className="ml-1 w-3 h-3" />
                          إعادة تعيين
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700"></div>
              <div className="overflow-x-auto rounded-b-xl">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase w-16">الترتيب</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">اسم المنتج</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الفئة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">اتجاه المبيعات</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">نقاط الأداء</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الكمية المباعة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الإيرادات</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الأرباح</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">التوصية</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {getFilteredPerformance().data.length > 0 ? (
                      getFilteredPerformance().data.map((item, index) => {
                        // حساب الترتيب الثابت بناءً على الصفحة الحالية والفهرس
                        const rank = (analyticsPagination.performance.currentPage - 1) * analyticsPagination.performance.itemsPerPage + index + 1;

                        return (
                          <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-semibold mx-auto">
                                {rank}
                              </div>
                            </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                            <div>
                              <div className="font-medium">{item.name}</div>
                              {item.barcode && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  {item.barcode}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {item.category || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                              item.sales_trend === 'increasing' ? 'bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300' :
                              item.sales_trend === 'stable' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                              item.sales_trend === 'decreasing' ? 'bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300' :
                              'bg-danger-100 text-danger-800 dark:bg-danger-900/30 dark:text-danger-300'
                            }`}>
                              {item.sales_trend === 'increasing' ? 'متزايد' :
                               item.sales_trend === 'stable' ? 'مستقر' :
                               item.sales_trend === 'decreasing' ? 'متناقص' : 'لا مبيعات'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div className="flex items-center space-x-3">
                              <span className="font-medium min-w-[2rem] text-center">{item.performance_score}</span>
                              <div className="flex-1 max-w-20">
                                <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700 overflow-hidden">
                                  <div
                                    className={`h-full rounded-full transition-all duration-300 ${
                                      item.performance_score >= 80 ? 'bg-green-500' :
                                      item.performance_score >= 60 ? 'bg-blue-500' :
                                      item.performance_score >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{
                                      width: `${Math.max(item.performance_score || 0, 0)}%`,
                                      minWidth: item.performance_score > 0 ? '4px' : '0px'
                                    }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {item.total_sold.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <FormattedCurrency amount={item.total_revenue} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-success-600 dark:text-success-400 font-medium">
                            <FormattedCurrency amount={item.total_profit} />
                          </td>
                          <td className="px-6 py-4 text-sm max-w-xs">
                            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-md p-2 border border-gray-200 dark:border-gray-700">
                              <div className="flex items-center gap-2">
                                {/* مؤشر الحالة */}
                                <div className={`flex-shrink-0 w-1 h-6 rounded-full ${
                                  item.performance_score >= 80
                                    ? 'bg-success-500'
                                    : item.performance_score >= 60
                                    ? 'bg-primary-500'
                                    : item.performance_score >= 40
                                    ? 'bg-warning-500'
                                    : 'bg-danger-500'
                                }`}></div>

                                {/* محتوى التوصية */}
                                <div className="flex-1 min-w-0">
                                  <div className="text-xs font-medium leading-snug text-gray-900 dark:text-gray-100">
                                    {item.recommendation}
                                  </div>
                                </div>

                                {/* تصنيف ذكي */}
                                <span className={`flex-shrink-0 inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                                  item.performance_score >= 80
                                    ? 'bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-300'
                                    : item.performance_score >= 60
                                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300'
                                    : item.performance_score >= 40
                                    ? 'bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-300'
                                    : 'bg-danger-100 text-danger-700 dark:bg-danger-900/30 dark:text-danger-300'
                                }`}>
                                  {(() => {
                                    const rec = item.recommendation.toLowerCase();
                                    if (rec.includes('زيادة') || rec.includes('تعزيز') || rec.includes('استمر')) {
                                      return 'تطوير';
                                    } else if (rec.includes('تقليل') || rec.includes('خفض') || rec.includes('تخفيض')) {
                                      return 'تحسين';
                                    } else if (rec.includes('مراقبة') || rec.includes('متابعة') || rec.includes('مراجعة')) {
                                      return 'مراقبة';
                                    } else if (rec.includes('ترويج') || rec.includes('تسويق') || rec.includes('عرض')) {
                                      return 'ترويج';
                                    } else if (rec.includes('إيقاف') || rec.includes('توقف') || rec.includes('استبدال')) {
                                      return 'إعادة تقييم';
                                    } else if (rec.includes('تحليل') || rec.includes('دراسة') || rec.includes('فحص')) {
                                      return 'تحليل';
                                    } else if (rec.includes('تحديث') || rec.includes('تطوير') || rec.includes('تحديث')) {
                                      return 'تحديث';
                                    } else if (item.performance_score >= 80) {
                                      return 'متميز';
                                    } else if (item.performance_score >= 60) {
                                      return 'مستقر';
                                    } else if (item.performance_score >= 40) {
                                      return 'تحسين';
                                    } else {
                                      return 'حرج';
                                    }
                                  })()}
                                </span>
                              </div>
                            </div>
                          </td>
                        </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={9} className="px-6 py-12 text-center">
                          <div className="flex flex-col items-center space-y-4">
                            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                              <FaChartLine className="text-2xl text-gray-400 dark:text-gray-500" />
                            </div>
                            <div>
                              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                                لا توجد نتائج مطابقة للفلاتر
                              </h3>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                جرب تعديل الفلاتر للحصول على نتائج مختلفة
                              </p>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <Pagination
                currentPage={analyticsPagination.performance.currentPage}
                totalPages={getFilteredPerformance().totalPages}
                itemsPerPage={analyticsPagination.performance.itemsPerPage}
                totalItems={getFilteredPerformance().total}
                onPageChange={(page) => handleAnalyticsPageChange('performance', page)}
                onItemsPerPageChange={(itemsPerPage) => handleAnalyticsItemsPerPageChange('performance', itemsPerPage)}
              />
            </div>
          )}

          {/* Empty States for tabs with no original data */}
          {analyticsTab === 'bestselling' && bestSellingProducts.length === 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-12 border border-gray-200 dark:border-gray-700 text-center">
              <FaTrophy className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                لا توجد بيانات مبيعات
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                لا توجد منتجات مباعة خلال فترة {selectedPeriod} يوم المحددة
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                جرب تغيير فترة التحليل إلى فترة أطول للحصول على بيانات أكثر
              </p>
            </div>
          )}

          {analyticsTab === 'unsold' && unsoldProducts.length === 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-12 border border-gray-200 dark:border-gray-700 text-center">
              <FaExclamationCircle className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                جميع المنتجات مباعة
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                جميع المنتجات حققت مبيعات خلال فترة {selectedPeriod} يوم المحددة
              </p>
            </div>
          )}

          {analyticsTab === 'losses' && expectedLosses.length === 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-12 border border-gray-200 dark:border-gray-700 text-center">
              <FaExclamationTriangle className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                لا توجد خسائر متوقعة
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                جميع المنتجات تحقق أداءً جيداً ولا توجد مخاطر خسارة خلال فترة {selectedPeriod} يوم
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                هذا مؤشر إيجابي على صحة المخزون وجودة إدارة المنتجات
              </p>
            </div>
          )}

          {analyticsTab === 'inventory' && inventoryStatus.length === 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-12 border border-gray-200 dark:border-gray-700 text-center">
              <FaWarehouse className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                لا توجد بيانات مخزون
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                لا توجد منتجات تحتاج إلى متابعة حالة المخزون بناءً على مبيعات {selectedPeriod} يوم
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                جميع المنتجات في حالة مخزون صحية أو جرب تغيير فترة التحليل
              </p>
            </div>
          )}

          {analyticsTab === 'performance' && performanceAnalysis.length === 0 && !analyticsInitialLoading && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-12 border border-gray-200 dark:border-gray-700 text-center">
              <FaChartLine className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                لا توجد بيانات أداء
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                لا توجد بيانات كافية لتحليل أداء المنتجات خلال فترة {selectedPeriod} يوم
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                جرب تغيير فترة التحليل أو تأكد من وجود مبيعات في النظام
              </p>
            </div>
          )}
        </div>
      )}

      {/* Product Form Modal */}
      <Modal
        isOpen={showForm}
        onClose={() => {
          setSelectedProduct(null);
          setShowForm(false);
        }}
        title={selectedProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        size="lg"
      >
        <ProductForm
          product={selectedProduct || undefined}
          onClose={() => {
            setSelectedProduct(null);
            setShowForm(false);
          }}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({
          isOpen: false,
          productId: null,
          productName: '',
          isLoading: false
        })}
        onConfirm={confirmDelete}
        title="تأكيد حذف المنتج"
        message="هل أنت متأكد من رغبتك في حذف هذا المنتج؟"
        itemName={deleteModal.productName}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({
          isOpen: false,
          title: '',
          message: ''
        })}
        title={successModal.title}
        message={successModal.message}
      />
    </div>
  );
};

export default Products;