import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaArrowLeft,
  FaSearch,
  FaFilter,
  FaPrint,
  FaFileDownload,
  FaEye,
  FaTrash,
  FaSync,
  FaExclamationTriangle,
  FaCheckCircle,
  FaChevronLeft,
  FaChevronRight,
  FaTrashAlt,
  FaShoppingCart,
  FaMoneyBillWave,
  FaListAlt,
  FaPercent,
  FaReceipt
} from 'react-icons/fa';
import { DatePicker, SelectInput, NumberInput } from '../components/inputs';
import api from '../lib/axios';
import { useAuthStore } from '../stores/authStore';
import { useTheme } from '../contexts/ThemeContext';
import { formatDate, formatTime } from '../utils/dateUtils';
import FormattedCurrency from '../components/FormattedCurrency';
import { numberFormattingService } from '../services/numberFormattingService';

interface Sale {
  id: number;
  total_amount: number;
  payment_method: string;
  amount_paid: number;  // Changed from payment_amount to amount_paid
  payment_status: string;  // Added payment_status
  change_amount: number;
  tax_amount: number;
  discount_amount: number;
  discount_type: string;
  created_at: string;
  items_count: number;
  debt_amount?: number;  // Added debt_amount
  cashier_name?: string;
  user_id?: number;
  customer_id?: number;  // Added customer_id
  customer_name?: string;  // Added customer_name
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
}

interface SaleDetail {
  id: number;
  total_amount: number;
  payment_method: string;
  amount_paid: number;  // Changed from payment_amount to amount_paid
  payment_status: string;  // Added payment_status
  change_amount: number;
  tax_amount: number;
  discount_amount: number;
  discount_type: string;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  items: SaleItem[];
  debt_amount?: number;  // Added debt_amount
  cashier_name?: string;
  customer_id?: number;  // Added customer_id
  customer_name?: string;  // Added customer_name
}

interface SaleItem {
  id: number;
  product_id: number;
  product_name: string;
  quantity: number;
  price: number;
  subtotal: number;
}

interface SalesFilter {
  startDate: string;
  endDate: string;
  minAmount: string;
  maxAmount: string;
  paymentMethod: 'all' | 'cash' | 'card' | 'آجل' | 'جزئي' | 'مختلط';
  cashierId: string;
  customerId: string;  // Added customer filter
  page: number;
  limit: number;
}

const initialFilter: SalesFilter = {
  startDate: '',
  endDate: '',
  minAmount: '',
  maxAmount: '',
  paymentMethod: 'all',
  cashierId: '',
  customerId: '',  // Added customer filter
  page: 1,
  limit: 10
};

const Sales: React.FC = () => {
  // Initialize arrays as empty to prevent undefined errors
  const [sales, setSales] = useState<Sale[]>([]);
  const [filteredSales, setFilteredSales] = useState<Sale[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Start with loading state

  // Track if initial load has been done (similar to Products.tsx)
  const initialLoadDone = useRef(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SalesFilter>(initialFilter);
  // Temporary filters that will be applied only when the user clicks "Apply Filters"
  const [tempFilters, setTempFilters] = useState<SalesFilter>(initialFilter);
  const [showSaleDetail, setShowSaleDetail] = useState(false);
  const [selectedSale, setSelectedSale] = useState<SaleDetail | null>(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });
  const [cashiers, setCashiers] = useState<{id: number, full_name: string}[]>([]);
  const [customers, setCustomers] = useState<{id: number, name: string}[]>([]);  // Added customers state
  // State for tracking selected sales for bulk deletion
  const [selectedSales, setSelectedSales] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // We don't need separate salesStats state anymore as we're calculating from filteredSales

  const { user } = useAuthStore();
  useTheme(); // Usar el hook para asegurar que los estilos dark mode se apliquen correctamente
  const navigate = useNavigate();

  // Hook للتنسيق المتزامن
  const [formatSettings, setFormatSettings] = useState<any>(null);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await numberFormattingService.getCurrentSettings();
        setFormatSettings(settings);
      } catch (error) {
        console.error('Error loading format settings:', error);
      }
    };
    loadSettings();
  }, []);

  // دالة تنسيق الأرقام بدون عملة
  const formatNumber = (amount: number): string => {
    if (!formatSettings) {
      return amount.toFixed(2); // fallback
    }

    const fixedAmount = formatSettings.showDecimals ?
      amount.toFixed(formatSettings.decimalPlaces) :
      Math.round(amount).toString();

    // تطبيق الفواصل
    let formattedNumber = fixedAmount;
    if (formatSettings.separatorType !== 'none') {
      const parts = fixedAmount.split('.');
      const integerPart = parts[0];
      const decimalPart = parts[1];
      const separator = formatSettings.separatorType === 'comma' ? ',' : ' ';
      const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
      formattedNumber = decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
    }

    return formattedNumber;
  };


  // Fetch initial data on component mount (similar to Products.tsx)
  useEffect(() => {
    // Prevent duplicate initial loads
    if (initialLoadDone.current) {
      return;
    }

    console.log('🚀 [SALES] Component mounted, initializing data...');

    const initializeData = async () => {
      try {
        // Fetch cashiers
        const fetchCashiers = async () => {
          try {
            // Try to get current user info first
            const currentUserResponse = await api.get('/api/auth/me');
            console.log('Current user:', currentUserResponse.data);

            // If user is admin, fetch all users
            if (currentUserResponse.data.role === 'admin') {
              try {
                const usersResponse = await api.get('/api/users/');
                console.log('All users:', usersResponse.data);

                // Map users to cashiers format
                const allCashiers = usersResponse.data.map((user: any) => ({
                  id: user.id,
                  full_name: user.full_name || user.username || 'User'
                }));

                setCashiers(allCashiers);
              } catch (error) {
                console.error('Error fetching all users:', error);
                // Fallback to just the current user if fetching all users fails
                setCashiers([{
                  id: currentUserResponse.data.id,
                  full_name: currentUserResponse.data.full_name || currentUserResponse.data.username || 'User'
                }]);
              }
            } else {
              // For non-admin users, just use the current user
              setCashiers([{
                id: currentUserResponse.data.id,
                full_name: currentUserResponse.data.full_name || currentUserResponse.data.username || 'User'
              }]);
            }
          } catch (error) {
            console.error('Error fetching current user:', error);
            // Use default cashiers as fallback
            setCashiers([{ id: 1, full_name: 'Admin' }]);
          }
        };

        // Fetch customers
        const fetchCustomers = async () => {
          try {
            const response = await api.get('/api/customers/');
            console.log('Customers:', response.data);

            // Map customers to the format we need
            const customersList = response.data.map((customer: any) => ({
              id: customer.id,
              name: customer.name || 'عميل غير معروف'
            }));

            setCustomers(customersList);
          } catch (error) {
            console.error('Error fetching customers:', error);
            // Set empty array as fallback
            setCustomers([]);
          }
        };

        // Execute all initial data fetching
        await Promise.all([
          fetchCashiers(),
          fetchCustomers()
        ]);

        // Initialize tempFilters with the current filters
        setTempFilters(filters);

        // Fetch sales data - ONLY ONCE during initialization
        console.log('🔄 [SALES] Fetching initial sales data...');
        await fetchSales(1, false);

        console.log('✅ [SALES] Initial load completed');

      } catch (error) {
        console.error('❌ [SALES] Error during initialization:', error);
      }
    };

    initializeData();

    // Mark initial load as done
    initialLoadDone.current = true;

    return () => {};
  }, []); // Empty dependency array - runs only once

  // Update local state when filters change (similar to Products.tsx)
  useEffect(() => {
    console.log('📊 [SALES] Filters changed:', filters);
    // Update tempFilters to keep them in sync
    setTempFilters(filters);
  }, [filters]);

  // Fetch sales from API with server-side pagination
  const fetchSales = async (pageNumber: number = filters.page, applyFilters: boolean = false, customFilters?: any) => {
    console.log(`🔄 [SALES] Starting fetchSales - Page: ${pageNumber}, ApplyFilters: ${applyFilters}`);

    setIsLoading(true);
    try {

      // Get current user info to check role
      const currentUserResponse = await api.get('/api/auth/me');
      const currentUser = currentUserResponse.data;
      const isAdmin = currentUser.role === 'admin';

      // Build API parameters for server-side filtering and pagination
      const params: any = {
        page: pageNumber,
        limit: filters.limit,
        _t: new Date().getTime().toString()
      };

      // Apply filters if requested
      if (applyFilters) {
        // Use custom filters if provided, otherwise use current filters
        const filtersToUse = customFilters || filters;

        if (filtersToUse.startDate) {
          params.start_date = filtersToUse.startDate;
        }
        if (filtersToUse.endDate) {
          params.end_date = filtersToUse.endDate;
        }
        if (filtersToUse.minAmount) {
          params.min_amount = filtersToUse.minAmount;
        }
        if (filtersToUse.maxAmount) {
          params.max_amount = filtersToUse.maxAmount;
        }
        if (filtersToUse.paymentMethod && filtersToUse.paymentMethod !== 'all') {
          params.payment_method = filtersToUse.paymentMethod;
        }
        if (filtersToUse.cashierId && isAdmin) {
          // Only apply cashier filter for admin users
          params.user_id = filtersToUse.cashierId;
        }
        if (filtersToUse.customerId) {
          params.customer_id = filtersToUse.customerId;
        }
        if (searchTerm) {
          params.search = searchTerm;
        }
      }

      // If not admin, filter by user_id (override any cashier filter)
      if (!isAdmin) {
        params.user_id = currentUser.id;
      }

      // Make request using axios to ensure authentication is handled correctly
      const response = await api.get('/api/sales/', {
        params
      });

      console.log('Raw API response data:', response.data);
      console.log('Response headers:', response.headers);

      // Use the data directly
      let salesData = response.data;
      console.log('Using data directly, length:', salesData.length);

      // Get pagination info from response headers
      const totalCount = parseInt(response.headers['x-total-count'] || '0');
      const currentPage = parseInt(response.headers['x-page'] || '1');
      const pageLimit = parseInt(response.headers['x-limit'] || filters.limit.toString());
      const totalPages = parseInt(response.headers['x-pages'] || '1');

      console.log('Pagination from server:', {
        totalCount,
        currentPage,
        pageLimit,
        totalPages
      });

      // Map the data to ensure it matches our interface
      const formattedSales = salesData.map((sale: any) => {
        // Calculate final amount and debt
        const finalAmount = sale.total_amount + (sale.tax_amount || 0) - (sale.discount_amount || 0);
        const amountPaid = sale.amount_paid || 0;
        const debtAmount = Math.max(0, finalAmount - amountPaid);

        return {
          id: sale.id,
          total_amount: typeof sale.total_amount === 'number' ? sale.total_amount :
                        (typeof sale.total_amount === 'string' ? parseFloat(sale.total_amount) : 0),
          payment_method: sale.payment_method || 'cash',
          amount_paid: typeof sale.amount_paid === 'number' ? sale.amount_paid :
                      (typeof sale.amount_paid === 'string' ? parseFloat(sale.amount_paid) : 0),
          payment_status: sale.payment_status || 'paid',
          change_amount: typeof sale.change_amount === 'number' ? sale.change_amount :
                        (typeof sale.change_amount === 'string' ? parseFloat(sale.change_amount) : 0),
          tax_amount: typeof sale.tax_amount === 'number' ? sale.tax_amount :
                     (typeof sale.tax_amount === 'string' ? parseFloat(sale.tax_amount) : 0),
          discount_amount: typeof sale.discount_amount === 'number' ? sale.discount_amount :
                          (typeof sale.discount_amount === 'string' ? parseFloat(sale.discount_amount) : 0),
          discount_type: sale.discount_type || 'fixed',
          created_at: sale.created_at || new Date().toISOString(),
          items_count: typeof sale.items_count === 'number' ? sale.items_count :
                      (sale.items && Array.isArray(sale.items) ? sale.items.length : 0),
          debt_amount: debtAmount,
          customer_id: sale.customer_id || null,
          customer_name: sale.customer_name || (sale.customer ? sale.customer.name : null),
          cashier_name: sale.cashier_name ||
                       (sale.user && sale.user.full_name ? sale.user.full_name :
                       (sale.user_name || ''))
        };
      });

      console.log('Formatted sales data:', formattedSales);

      // Update state with server-side paginated data
      setSales(formattedSales);
      setFilteredSales(formattedSales);

      // Create pagination info from server response
      const paginationInfo = {
        total: totalCount,
        page: currentPage,
        limit: pageLimit,
        pages: totalPages
      };

      console.log('📊 [SALES] Server pagination info:', paginationInfo);

      setPagination(paginationInfo);
      setIsLoading(false);
      console.log('✅ [SALES] fetchSales completed successfully');
    } catch (error) {
      console.error('❌ [SALES] Error fetching sales:', error);
      setErrorMessage('فشل في تحميل المبيعات. يرجى المحاولة مرة أخرى.');
      // Initialize with empty arrays to prevent undefined errors
      setSales([]);
      setFilteredSales([]);
      setIsLoading(false);

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Refresh sales data
  const refreshSales = () => {
    fetchSales(1, true);
  };



  // Manual handlers will be added when needed for pagination and search

  // Reset filters
  const resetFilters = () => {
    console.log('Resetting all filters to initial state');
    // Reset both filters and tempFilters
    setFilters(initialFilter);
    setTempFilters(initialFilter);
    setSearchTerm('');

    // Fetch fresh data from server without filters
    fetchSales(1, false);
  };

  // Get sale details
  const getSaleDetails = async (saleId: number) => {
    try {
      console.log(`Fetching details for sale ID: ${saleId}`);
      const response = await api.get(`/api/sales/${saleId}`);
      console.log('Sale details response:', response.data);

      // Format the sale details to match our interface
      let saleData = response.data;

      // Handle different response formats
      if (!saleData) {
        throw new Error('No sale data returned from API');
      }

      // Calculate final amount and debt for detail view
      const finalAmount = saleData.total_amount + (saleData.tax_amount || 0) - (saleData.discount_amount || 0);
      const amountPaid = saleData.amount_paid || 0;
      const debtAmount = Math.max(0, finalAmount - amountPaid);

      // Create a properly formatted sale detail object
      const formattedSale: SaleDetail = {
        id: saleData.id,
        total_amount: saleData.total_amount || 0,
        payment_method: saleData.payment_method || 'cash',
        amount_paid: saleData.amount_paid || 0,
        payment_status: saleData.payment_status || 'paid',
        change_amount: saleData.change_amount || 0,
        tax_amount: saleData.tax_amount || 0,
        discount_amount: saleData.discount_amount || 0,
        discount_type: saleData.discount_type || 'fixed',
        created_at: saleData.created_at || new Date().toISOString(),
        updated_at: saleData.updated_at || null,
        created_by: saleData.created_by || saleData.user_id || 0,
        debt_amount: debtAmount,
        customer_id: saleData.customer_id || null,
        customer_name: saleData.customer_name || (saleData.customer ? saleData.customer.name : null),
        items: Array.isArray(saleData.items) ? saleData.items.map((item: any) => ({
          id: item.id,
          product_id: item.product_id,
          product_name: item.product && item.product.name ? item.product.name : 'منتج غير معروف',
          quantity: item.quantity || 0,
          price: item.unit_price || item.price || 0,
          subtotal: item.subtotal || (item.quantity * (item.unit_price || item.price)) || 0
        })) : [],
        cashier_name: saleData.cashier_name || (saleData.user ? saleData.user.full_name : '')
      };

      console.log('Formatted sale details:', formattedSale);
      setSelectedSale(formattedSale);
      setShowSaleDetail(true);
    } catch (error) {
      console.error('Error fetching sale details:', error);
      setErrorMessage('فشل في تحميل تفاصيل البيع. يرجى المحاولة مرة أخرى.');

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Delete single sale
  const handleDeleteSale = async (saleId: number, saleUserId?: number) => {
    // Check if user has permission to delete this sale
    if (user?.role !== 'admin') {
      // For non-admin users, check if the sale belongs to them
      if (saleUserId && saleUserId !== user?.id) {
        setErrorMessage('ليس لديك صلاحية لحذف هذا السجل');
        setTimeout(() => setErrorMessage(''), 3000);
        return;
      }
    }

    if (!window.confirm('هل أنت متأكد من حذف هذا البيع؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      return;
    }

    try {
      await api.delete(`/api/sales/${saleId}`);

      // Update sales list
      const updatedSales = sales.filter(sale => sale.id !== saleId);
      setSales(updatedSales);

      // Also update filtered sales
      setFilteredSales(filteredSales.filter(sale => sale.id !== saleId));

      // Clear from selected sales if it was selected
      setSelectedSales(selectedSales.filter(id => id !== saleId));

      // We don't need to update sales statistics here anymore
      // as we're calculating them directly from filteredSales in the render

      // Update pagination info
      const totalCount = updatedSales.length;
      const totalPages = Math.max(1, Math.ceil(totalCount / pagination.limit));

      // Adjust current page if needed
      let currentPage = pagination.page;
      if (currentPage > totalPages && totalPages > 0) {
        currentPage = totalPages;
      }

      setPagination({
        ...pagination,
        total: totalCount,
        pages: totalPages,
        page: currentPage
      });

      setSuccessMessage('تم حذف البيع بنجاح');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error deleting sale:', error);
      setErrorMessage('فشل في حذف البيع. يرجى المحاولة مرة أخرى.');

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Delete multiple sales
  const handleDeleteMultipleSales = async () => {
    if (selectedSales.length === 0) {
      setErrorMessage('لم يتم تحديد أي سجلات للحذف');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    setShowDeleteConfirmation(true);
  };

  // Confirm and execute multiple deletion
  const confirmDeleteMultipleSales = async () => {
    try {
      setIsLoading(true);

      // For non-admin users, filter out sales that don't belong to them
      let salesToDelete = [...selectedSales];

      if (user?.role !== 'admin') {
        // Filter sales to only include those that belong to the current user
        const userSales = sales.filter(sale => {
          // Extract user_id from sale object
          const saleUserId = sale.user_id ||
                            (sale.user && sale.user.id) ||
                            (typeof sale.cashier_name === 'string' &&
                             cashiers.find(c => c.full_name === sale.cashier_name)?.id);

          return saleUserId === user?.id;
        });

        // Get IDs of user's sales
        const userSaleIds = userSales.map(sale => sale.id);

        // Filter selected sales to only include those that belong to the user
        salesToDelete = selectedSales.filter(id => userSaleIds.includes(id));

        // If no sales are left after filtering, show error and return
        if (salesToDelete.length === 0) {
          setErrorMessage('ليس لديك صلاحية لحذف السجلات المحددة');
          setIsLoading(false);
          setShowDeleteConfirmation(false);
          setTimeout(() => setErrorMessage(''), 3000);
          return;
        }

        // If some sales were filtered out, show warning
        if (salesToDelete.length < selectedSales.length) {
          console.warn(`User can only delete ${salesToDelete.length} out of ${selectedSales.length} selected sales`);
        }
      }

      // Delete each selected sale
      for (const saleId of salesToDelete) {
        await api.delete(`/api/sales/${saleId}`);
      }

      // Update sales list
      const updatedSales = sales.filter(sale => !salesToDelete.includes(sale.id));
      setSales(updatedSales);

      // Also update filtered sales
      setFilteredSales(filteredSales.filter(sale => !salesToDelete.includes(sale.id)));

      // Update pagination info
      const totalCount = updatedSales.length;
      const totalPages = Math.max(1, Math.ceil(totalCount / pagination.limit));

      // Adjust current page if needed
      let currentPage = pagination.page;
      if (currentPage > totalPages && totalPages > 0) {
        currentPage = totalPages;
      }

      setPagination({
        ...pagination,
        total: totalCount,
        pages: totalPages,
        page: currentPage
      });

      // We don't need to update sales statistics here anymore
      // as we're calculating them directly from filteredSales in the render

      // Clear selected sales
      setSelectedSales([]);
      setSelectAll(false);
      setShowDeleteConfirmation(false);

      setSuccessMessage(`تم حذف ${salesToDelete.length} سجل بنجاح`);
      setIsLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error deleting multiple sales:', error);
      setErrorMessage('فشل في حذف بعض السجلات. يرجى المحاولة مرة أخرى.');
      setIsLoading(false);
      setShowDeleteConfirmation(false);

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Toggle select all sales
  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedSales([]);
    } else {
      // For admin, select all sales
      // For regular users, select only their own sales
      if (user?.role === 'admin') {
        setSelectedSales(filteredSales.map(sale => sale.id));
      } else {
        // Filter sales that belong to the current user
        const userSales = filteredSales.filter(sale =>
          sale.user_id === user?.id ||
          (sale.user && sale.user.id === user?.id) ||
          (sale.cashier_name && sale.cashier_name.includes(user?.full_name || ''))
        );
        setSelectedSales(userSales.map(sale => sale.id));
      }
    }
    setSelectAll(!selectAll);
  };

  // Toggle select single sale
  const toggleSelectSale = (saleId: number) => {
    if (selectedSales.includes(saleId)) {
      setSelectedSales(selectedSales.filter(id => id !== saleId));
      setSelectAll(false);
    } else {
      setSelectedSales([...selectedSales, saleId]);
      // Check if all sales are now selected
      if (selectedSales.length + 1 === filteredSales.length) {
        setSelectAll(true);
      }
    }
  };

  // Print sale receipt
  const printReceipt = (saleId: number) => {
    navigate(`/sales/${saleId}/print`);
  };

  // Export sales to CSV
  const exportToCSV = () => {
    // Check if filteredSales exists and has items
    if (!filteredSales || filteredSales.length === 0) {
      setErrorMessage('لا توجد بيانات للتصدير');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    try {
      // Create CSV content with new columns
      const headers = 'رقم البيع,التاريخ,سعر المنتجات,الخصم,الضريبة,إجمالي الفاتورة,المبلغ المدفوع,قيمة الدين,طريقة الدفع,حالة الدفع,عدد المنتجات,الكاشير,العميل\n';
      const rows = filteredSales.map(sale => {
        const finalAmount = sale.total_amount + (sale.tax_amount || 0) - (sale.discount_amount || 0);
        const paymentMethodText = sale.payment_method === 'آجل' ? 'آجل' :
                                 sale.payment_method === 'جزئي' ? 'جزئي' :
                                 sale.payment_method === 'cash' ? 'نقدي' : 'بطاقة';
        const paymentStatusText = sale.payment_status === 'paid' ? 'مدفوع كاملاً' :
                                 sale.payment_status === 'partial' ? 'مدفوع جزئياً' : 'غير مدفوع';

        return `${sale.id},"${new Date(sale.created_at).toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })}",${sale.total_amount.toFixed(2)},${(sale.discount_amount || 0).toFixed(2)},${(sale.tax_amount || 0).toFixed(2)},${finalAmount.toFixed(2)},${(sale.amount_paid || 0).toFixed(2)},${(sale.debt_amount || 0).toFixed(2)},"${paymentMethodText}","${paymentStatusText}",${sale.items_count},"${sale.cashier_name || ''}","${sale.customer_name || ''}"`;
      }).join('\n');

      const csvContent = `data:text/csv;charset=utf-8,${headers}${rows}`;

      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `sales_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);

      // Trigger download and clean up
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      setErrorMessage('حدث خطأ أثناء تصدير البيانات');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Using formatDate and formatTime from dateUtils

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaShoppingCart className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">سجل المبيعات</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  عرض وإدارة جميع عمليات البيع
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap">
              <button
                onClick={refreshSales}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 shadow-lg hover:shadow-xl"
                title="تحديث البيانات"
              >
                <FaSync className={`text-sm ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={() => navigate('/pos')}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FaShoppingCart className="ml-2 text-sm" />
                <span className="hidden sm:inline lg:inline">عملية بيع جديدة</span>
                <span className="sm:hidden lg:hidden">بيع جديد</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {/* Sales Count */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaListAlt className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">عدد المبيعات</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{filteredSales.length}</div>
            </div>
          </div>

          {/* Total Products Amount */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/20 rounded-lg border border-gray-100 dark:border-gray-600/30">
            <div className="bg-gray-100 dark:bg-gray-700/40 p-2.5 rounded-lg flex-shrink-0">
              <FaShoppingCart className="text-gray-600 dark:text-gray-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">إجمالي سعر المنتجات</div>
              <div className="text-lg font-bold text-gray-600 dark:text-gray-400 truncate">
                {filteredSales.reduce((sum: number, sale: Sale) => sum + sale.total_amount, 0).toFixed(2)} د.ل
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">قبل الخصم والضريبة</div>
            </div>
          </div>

          {/* Total Discounts */}
          <div className="flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-100 dark:border-orange-800/30">
            <div className="bg-orange-100 dark:bg-orange-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaPercent className="text-orange-600 dark:text-orange-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-orange-700 dark:text-orange-300 mb-1">إجمالي الخصومات</div>
              <div className="text-lg font-bold text-orange-600 dark:text-orange-400 truncate">
                -{filteredSales.reduce((sum: number, sale: Sale) => sum + (sale.discount_amount || 0), 0).toFixed(2)} د.ل
              </div>
              <div className="text-xs text-orange-500 dark:text-orange-400">
                {(() => {
                  const totalProducts = filteredSales.reduce((sum: number, sale: Sale) => sum + sale.total_amount, 0);
                  const totalDiscounts = filteredSales.reduce((sum: number, sale: Sale) => sum + (sale.discount_amount || 0), 0);
                  const discountPercentage = totalProducts > 0 ? ((totalDiscounts / totalProducts) * 100) : 0;
                  return `(${discountPercentage.toFixed(1)}%)`;
                })()}
              </div>
            </div>
          </div>

          {/* Total Taxes */}
          <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaReceipt className="text-blue-600 dark:text-blue-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">إجمالي الضرائب</div>
              <div className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">
                +{filteredSales.reduce((sum: number, sale: Sale) => sum + (sale.tax_amount || 0), 0).toFixed(2)} د.ل
              </div>
              <div className="text-xs text-blue-500 dark:text-blue-400">
                {(() => {
                  const totalAfterDiscounts = filteredSales.reduce((sum: number, sale: Sale) => {
                    return sum + (sale.total_amount - (sale.discount_amount || 0));
                  }, 0);
                  const totalTaxes = filteredSales.reduce((sum: number, sale: Sale) => sum + (sale.tax_amount || 0), 0);
                  const taxPercentage = totalAfterDiscounts > 0 ? ((totalTaxes / totalAfterDiscounts) * 100) : 0;
                  return `(${taxPercentage.toFixed(1)}%)`;
                })()}
              </div>
            </div>
          </div>

          {/* Amount Paid */}
          <div className="flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30">
            <div className="bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-success-600 dark:text-success-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-success-700 dark:text-success-300 mb-1">المبلغ المدفوع فعلياً</div>
              <div className="text-lg font-bold text-success-600 dark:text-success-400 truncate">
                <FormattedCurrency
                  amount={filteredSales.reduce((sum: number, sale: Sale) => sum + (sale.amount_paid || 0), 0)}
                />
              </div>
              <div className="text-xs text-success-500 dark:text-success-400">المبلغ المحصل</div>
            </div>
          </div>

          {/* Total Debts */}
          <div className="flex items-center gap-3 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-100 dark:border-danger-800/30">
            <div className="bg-danger-100 dark:bg-danger-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaExclamationTriangle className="text-danger-600 dark:text-danger-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-danger-700 dark:text-danger-300 mb-1">الديون المستحقة</div>
              <div className="text-lg font-bold text-danger-600 dark:text-danger-400 truncate">
                <FormattedCurrency
                  amount={filteredSales.reduce((sum: number, sale: Sale) => sum + (sale.debt_amount || 0), 0)}
                />
              </div>
              <div className="text-xs text-danger-500 dark:text-danger-400">
                {(() => {
                  const totalDebts = filteredSales.reduce((sum: number, sale: Sale) => sum + (sale.debt_amount || 0), 0);
                  const totalInvoices = filteredSales.reduce((sum: number, sale: Sale) => {
                    const finalAmount = sale.total_amount + (sale.tax_amount || 0) - (sale.discount_amount || 0);
                    return sum + finalAmount;
                  }, 0);
                  const debtPercentage = totalInvoices > 0 ? ((totalDebts / totalInvoices) * 100) : 0;
                  return `(${debtPercentage.toFixed(1)}%)`;
                })()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      {successMessage && (
        <div className="bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300 p-3 rounded-xl mb-4 flex items-center">
          <FaCheckCircle className="mr-2 flex-shrink-0" />
          {successMessage}
        </div>
      )}

      {errorMessage && (
        <div className="bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-3 rounded-xl mb-4 flex items-center">
          <FaExclamationTriangle className="mr-2 flex-shrink-0" />
          {errorMessage}
        </div>
      )}

      {/* Search & Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="number"
                placeholder="بحث برقم البيع فقط..."
                value={searchTerm}
                onChange={(e) => {
                  // السماح بالأرقام فقط
                  const value = e.target.value;
                  if (value === '' || /^\d+$/.test(value)) {
                    setSearchTerm(value);
                  }
                }}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>

            <button
              onClick={exportToCSV}
              disabled={!filteredSales || filteredSales.length === 0}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                !filteredSales || filteredSales.length === 0
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border border-gray-200 dark:border-gray-600 cursor-not-allowed'
                  : 'bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300 border border-success-200 dark:border-success-800 hover:bg-success-200 dark:hover:bg-success-900/50'
              }`}
            >
              <FaFileDownload className="ml-2" />
              تصدير CSV
            </button>
            <button
              onClick={handleDeleteMultipleSales}
              disabled={selectedSales.length === 0}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                selectedSales.length === 0
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border border-gray-200 dark:border-gray-600 cursor-not-allowed'
                  : 'bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 border border-danger-200 dark:border-danger-800 hover:bg-danger-200 dark:hover:bg-danger-900/50'
              }`}
            >
              <FaTrashAlt className="ml-2" />
              حذف المحدد ({selectedSales.length})
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Range Filters */}
              <div className="sm:col-span-1 lg:col-span-1">
                <DatePicker
                  label="تاريخ البدء"
                  name="startDate"
                  value={tempFilters.startDate}
                  onChange={(date) => {
                    setTempFilters({
                      ...tempFilters,
                      startDate: date
                    });
                  }}
                  placeholder="اختر تاريخ البدء"
                />
              </div>

              <div className="sm:col-span-1 lg:col-span-1">
                <DatePicker
                  label="تاريخ الانتهاء"
                  name="endDate"
                  value={tempFilters.endDate}
                  onChange={(date) => {
                    setTempFilters({
                      ...tempFilters,
                      endDate: date
                    });
                  }}
                  placeholder="اختر تاريخ الانتهاء"
                />
              </div>

              {/* Payment Method Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectInput
                  label="طريقة الدفع"
                  name="paymentMethod"
                  value={tempFilters.paymentMethod}
                  onChange={(value: string) => {
                    setTempFilters({
                      ...tempFilters,
                      paymentMethod: value as 'all' | 'cash' | 'card' | 'آجل' | 'جزئي' | 'مختلط'
                    });
                  }}
                  options={[
                    { value: 'all', label: 'الجميع' },
                    { value: 'cash', label: 'نقدي' },
                    { value: 'card', label: 'بطاقة' },
                    { value: 'آجل', label: 'آجل' },
                    { value: 'جزئي', label: 'جزئي' },
                    { value: 'مختلط', label: 'مختلط' }
                  ]}
                  placeholder="اختر طريقة الدفع..."
                />
              </div>

              {/* Items Per Page Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectInput
                  label="عدد العناصر"
                  name="limit"
                  value={filters.limit.toString()}
                  onChange={(value: string) => {
                    const newLimit = parseInt(value, 10);
                    setFilters({
                      ...filters,
                      limit: newLimit,
                      page: 1
                    });
                    setTempFilters({
                      ...tempFilters,
                      limit: newLimit,
                      page: 1
                    });
                  }}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                  placeholder="اختر عدد العناصر..."
                />
              </div>

              {/* Cashier Filter - Only for admin users */}
              {user?.role === 'admin' && (
                <div className="sm:col-span-1 lg:col-span-1">
                  <SelectInput
                    label="الكاشير"
                    name="cashierId"
                    value={tempFilters.cashierId}
                    onChange={(value: string) => {
                      setTempFilters({
                        ...tempFilters,
                        cashierId: value
                      });
                    }}
                    options={[
                      { value: '', label: 'جميع الكاشيرية' },
                      ...cashiers.map(cashier => ({
                        value: cashier.id.toString(),
                        label: cashier.full_name
                      }))
                    ]}
                    placeholder="اختر الكاشير..."
                  />
                </div>
              )}

              {/* Customer Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectInput
                  label="العميل"
                  name="customerId"
                  value={tempFilters.customerId}
                  onChange={(value: string) => {
                    setTempFilters({
                      ...tempFilters,
                      customerId: value
                    });
                  }}
                  options={[
                    { value: '', label: 'جميع العملاء' },
                    ...customers.map(customer => ({
                      value: customer.id.toString(),
                      label: customer.name
                    }))
                  ]}
                  placeholder="اختر العميل..."
                />
              </div>

              {/* Amount Range Filters */}
              <div className="sm:col-span-1 lg:col-span-1">
                <NumberInput
                  label="الحد الأدنى للمبلغ"
                  name="minAmount"
                  value={tempFilters.minAmount}
                  onChange={(value) => {
                    setTempFilters({
                      ...tempFilters,
                      minAmount: value
                    });
                  }}
                  step="0.01"
                  min={0}
                  placeholder="0.00"
                  dir="ltr"
                />
              </div>

              <div className="sm:col-span-1 lg:col-span-1">
                <NumberInput
                  label="الحد الأعلى للمبلغ"
                  name="maxAmount"
                  value={tempFilters.maxAmount}
                  onChange={(value) => {
                    setTempFilters({
                      ...tempFilters,
                      maxAmount: value
                    });
                  }}
                  step="0.01"
                  min={0}
                  placeholder="0.00"
                  dir="ltr"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-4 mt-6">
              <button
                type="button"
                onClick={resetFilters}
                className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
              >
                إعادة تعيين
              </button>
              <button
                type="button"
                onClick={() => {
                  console.log('Applying filters and resetting to page 1');
                  const newFilters = {...tempFilters, page: 1};
                  setFilters(newFilters);
                  setSearchTerm('');

                  // Fetch data from server with new filters
                  fetchSales(1, true, newFilters);
                }}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20"
              >
                تطبيق الفلاتر
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Sales Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-600 dark:border-primary-400"></div>
          </div>
        ) : filteredSales && filteredSales.length > 0 ? (
          <>
            <div className="overflow-x-auto custom-scrollbar">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    {/* Checkbox column for all users */}
                    <th scope="col" className="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={toggleSelectAll}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                      />
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      رقم البيع
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      الوقت
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      سعر المنتجات
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      الخصم
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      الضريبة
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      المبلغ المستلم
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      قيمة الدين
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      طريقة الدفع
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      عدد المنتجات
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      الكاشير
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      العميل
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredSales.map((sale) => (
                    <tr key={sale.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      {/* Checkbox for all users, but non-admin users can only select their own sales */}
                      <td className="px-3 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={selectedSales.includes(sale.id)}
                          onChange={() => toggleSelectSale(sale.id)}
                          disabled={user?.role !== 'admin' &&
                                  !(sale.user_id === user?.id ||
                                    (sale.user && sale.user.id === user?.id) ||
                                    (sale.cashier_name && sale.cashier_name.includes(user?.full_name || '')))}
                          className={`h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded
                            ${user?.role !== 'admin' &&
                              !(sale.user_id === user?.id ||
                                (sale.user && sale.user.id === user?.id) ||
                                (sale.cashier_name && sale.cashier_name.includes(user?.full_name || '')))
                              ? 'opacity-50 cursor-not-allowed' : ''}`}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">#{sale.id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">{formatDate(sale.created_at)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">{formatTime(sale.created_at)}</div>
                      </td>
                      {/* سعر المنتجات (قبل الخصم والضريبة) */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          <FormattedCurrency amount={sale.total_amount} />
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          سعر المنتجات فقط
                        </div>
                      </td>

                      {/* الخصم */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {sale.discount_amount > 0 ? (
                            <div>
                              <span className="text-orange-600 dark:text-orange-400 font-medium">
                                -<FormattedCurrency amount={sale.discount_amount} />
                              </span>
                              {sale.discount_type === 'percentage' && (
                                <div className="text-xs text-orange-500 dark:text-orange-400">
                                  ({((sale.discount_amount / sale.total_amount) * 100).toFixed(1)}%)
                                </div>
                              )}
                              {sale.discount_type === 'fixed' && (
                                <div className="text-xs text-orange-500 dark:text-orange-400">
                                  مبلغ ثابت
                                </div>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-400 dark:text-gray-500">لا يوجد</span>
                          )}
                        </div>
                      </td>

                      {/* الضريبة */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {sale.tax_amount > 0 ? (
                            <div>
                              <span className="text-blue-600 dark:text-blue-400 font-medium">
                                +<FormattedCurrency amount={sale.tax_amount} />
                              </span>
                              <div className="text-xs text-blue-500 dark:text-blue-400">
                                {(() => {
                                  const totalAfterDiscount = sale.total_amount - (sale.discount_amount || 0);
                                  const taxRate = totalAfterDiscount > 0 ? ((sale.tax_amount / totalAfterDiscount) * 100) : 0;
                                  return `(${taxRate.toFixed(1)}%)`;
                                })()}
                              </div>
                            </div>
                          ) : (
                            <span className="text-gray-400 dark:text-gray-500">لا يوجد</span>
                          )}
                        </div>
                      </td>

                      {/* المبلغ المستلم (المدفوع فعلياً) */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-bold text-success-600 dark:text-success-400">
                          <FormattedCurrency amount={sale.amount_paid} />
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          المبلغ المدفوع فعلياً
                        </div>
                        {sale.payment_status && (
                          <div className={`text-xs mt-1 ${
                            sale.payment_status === 'paid' ? 'text-success-600 dark:text-success-400' :
                            sale.payment_status === 'partial' ? 'text-warning-600 dark:text-warning-400' :
                            'text-danger-600 dark:text-danger-400'
                          }`}>
                            {sale.payment_status === 'paid' ? 'مدفوع كاملاً' :
                             sale.payment_status === 'partial' ? 'مدفوع جزئياً' :
                             'غير مدفوع'}
                          </div>
                        )}
                      </td>

                      {/* قيمة الدين */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-bold ${
                          (sale.debt_amount || 0) > 0
                            ? 'text-danger-600 dark:text-danger-400'
                            : 'text-gray-400 dark:text-gray-500'
                        }`}>
                          {(sale.debt_amount || 0) > 0
                            ? <FormattedCurrency amount={sale.debt_amount!} />
                            : 'لا يوجد'
                          }
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {(sale.debt_amount || 0) > 0 ? 'مبلغ مستحق' : 'لا توجد ديون'}
                        </div>
                        {sale.customer_name && (sale.debt_amount || 0) > 0 && (
                          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            على: {sale.customer_name}
                          </div>
                        )}
                      </td>

                      {/* طريقة الدفع */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          sale.payment_method === 'آجل'
                            ? 'bg-danger-100 dark:bg-danger-900/30 text-danger-800 dark:text-danger-300'
                            : sale.payment_method === 'جزئي'
                            ? 'bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300'
                            : sale.payment_method === 'مختلط'
                            ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300'
                            : sale.payment_method === 'cash' || sale.payment_method === 'نقدي'
                            ? 'bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300'
                            : 'bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300'
                        }`}>
                          {sale.payment_method === 'آجل' ? 'آجل' :
                           sale.payment_method === 'جزئي' ? 'جزئي' :
                           sale.payment_method === 'مختلط' ? 'مختلط' :
                           sale.payment_method === 'cash' ? 'نقدي' :
                           sale.payment_method === 'card' ? 'بطاقة' :
                           sale.payment_method === 'نقدي' ? 'نقدي' :
                           sale.payment_method === 'بطاقة' ? 'بطاقة' : sale.payment_method}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {sale.items_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {sale.cashier_name || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {sale.customer_name || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center gap-3">
                          <button
                            onClick={() => getSaleDetails(sale.id)}
                            className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300"
                            title="عرض التفاصيل"
                          >
                            <FaEye />
                          </button>
                          <button
                            onClick={() => printReceipt(sale.id)}
                            className="text-success-600 dark:text-success-400 hover:text-success-800 dark:hover:text-success-300"
                            title="طباعة الفاتورة"
                          >
                            <FaPrint />
                          </button>
                          {/* Show delete button for admin or if the sale belongs to the current user */}
                          {(user?.role === 'admin' ||
                            (sale.user_id === user?.id) ||
                            (sale.user && sale.user.id === user?.id) ||
                            (sale.cashier_name && sale.cashier_name.includes(user?.full_name || ''))) && (
                            <button
                              onClick={() => handleDeleteSale(sale.id, sale.user_id || (sale.user && sale.user.id))}
                              className="text-danger-600 dark:text-danger-400 hover:text-danger-800 dark:hover:text-danger-300"
                              title="حذف"
                            >
                              <FaTrash />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination info is now shown in the pagination buttons section */}

            {/* Pagination buttons - always shown when there are sales */}
            {pagination && pagination.total > 0 && (
              <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="flex-1 flex justify-between items-center sm:hidden">
                  <button
                    onClick={() => {
                      const newPage = Math.max(1, pagination.page - 1);
                      setFilters({...filters, page: newPage});
                    }}
                    disabled={pagination.page <= 1}
                    className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                      pagination.page <= 1
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    السابق
                  </button>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>
                  </div>

                  <button
                    onClick={() => {
                      const newPage = Math.min(pagination.pages, pagination.page + 1);
                      setFilters({...filters, page: newPage});
                    }}
                    disabled={pagination.page >= pagination.pages}
                    className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                      pagination.page >= pagination.pages
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    التالي
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>{' '}
                      <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {pagination.total})</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
                      <button
                        onClick={() => {
                          const newPage = Math.max(1, pagination.page - 1);
                          setFilters({...filters, page: newPage});
                        }}
                        disabled={pagination.page <= 1}
                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                          pagination.page <= 1
                            ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                            : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        <span className="sr-only">السابق</span>
                        <FaChevronRight className="h-5 w-5" />
                      </button>

                      {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                        // Show pages around current page
                        let pageNum;
                        if (pagination.pages <= 5) {
                          // If 5 or fewer pages, show all
                          pageNum = i + 1;
                        } else if (pagination.page <= 3) {
                          // If near start, show first 5
                          pageNum = i + 1;
                        } else if (pagination.page >= pagination.pages - 2) {
                          // If near end, show last 5
                          pageNum = pagination.pages - 4 + i;
                        } else {
                          // Otherwise show current and 2 on each side
                          pageNum = pagination.page - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => {
                              setFilters({...filters, page: pageNum});
                            }}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              pagination.page === pageNum
                                ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300'
                                : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}

                      <button
                        onClick={() => {
                          const newPage = Math.min(pagination.pages, pagination.page + 1);
                          setFilters({...filters, page: newPage});
                        }}
                        disabled={pagination.page >= pagination.pages}
                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                          pagination.page >= pagination.pages
                            ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                            : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        <span className="sr-only">التالي</span>
                        <FaChevronLeft className="h-5 w-5" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <>
            <div className="text-center py-12">
              <div className="text-gray-500 dark:text-gray-400 mb-4">لا توجد مبيعات تطابق معايير البحث</div>
              <button
                onClick={resetFilters}
                className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 px-6 py-3 border-2 border-primary-200 dark:border-primary-800 rounded-xl transition-all duration-200 ease-in-out hover:border-primary-300 dark:hover:border-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-500/20"
              >
                إعادة ضبط الفلاتر
              </button>
            </div>

            {/* Pagination info is now shown in the pagination buttons section */}

            {/* Pagination buttons - even when no results */}
            {pagination && pagination.total > 0 && (
              <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>{' '}
                      <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {pagination.total})</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
                      <button
                        onClick={() => {
                          const newPage = Math.max(1, pagination.page - 1);
                          setFilters({...filters, page: newPage});
                        }}
                        disabled={pagination.page <= 1}
                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                          pagination.page <= 1
                            ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                            : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        <span className="sr-only">السابق</span>
                        <FaChevronRight className="h-5 w-5" />
                      </button>

                      {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                        // Show pages around current page
                        let pageNum;
                        if (pagination.pages <= 5) {
                          // If 5 or fewer pages, show all
                          pageNum = i + 1;
                        } else if (pagination.page <= 3) {
                          // If near start, show first 5
                          pageNum = i + 1;
                        } else if (pagination.page >= pagination.pages - 2) {
                          // If near end, show last 5
                          pageNum = pagination.pages - 4 + i;
                        } else {
                          // Otherwise show current and 2 on each side
                          pageNum = pagination.page - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => {
                              setFilters({...filters, page: pageNum});
                            }}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              pagination.page === pageNum
                                ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300'
                                : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}

                      <button
                        onClick={() => {
                          const newPage = Math.min(pagination.pages, pagination.page + 1);
                          setFilters({...filters, page: newPage});
                        }}
                        disabled={pagination.page >= pagination.pages}
                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                          pagination.page >= pagination.pages
                            ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                            : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        <span className="sr-only">التالي</span>
                        <FaChevronLeft className="h-5 w-5" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>



      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md mx-4 animate-fadeIn">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-medium text-secondary-900 dark:text-secondary-100">تأكيد الحذف</h3>
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-2xl"
              >
                &times;
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 dark:text-gray-300 mb-4">
                هل أنت متأكد من حذف {selectedSales.length} سجل؟ هذا الإجراء لا يمكن التراجع عنه.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <p>ملاحظة: سيتم استعادة كميات المنتجات المباعة إلى المخزون.</p>
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
              >
                إلغاء
              </button>
              <button
                onClick={confirmDeleteMultipleSales}
                className="bg-danger-600 hover:bg-danger-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-danger-600 hover:border-danger-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-danger-500/20 shadow-lg hover:shadow-xl gap-2"
              >
                <FaTrash />
                <span>تأكيد الحذف</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Sale Detail Modal */}
      {showSaleDetail && selectedSale && (
        <div className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-2xl mx-4 animate-fadeIn">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-medium text-secondary-900 dark:text-secondary-100">تفاصيل البيع #{selectedSale.id}</h3>
              <button
                onClick={() => setShowSaleDetail(false)}
                className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-2xl"
              >
                &times;
              </button>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">التاريخ:</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">{formatDate(selectedSale.created_at)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">الوقت:</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">{formatTime(selectedSale.created_at)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">طريقة الدفع:</p>
                <p className={`font-medium ${
                  selectedSale.payment_method === 'آجل'
                    ? 'text-danger-600 dark:text-danger-400'
                    : selectedSale.payment_method === 'جزئي'
                    ? 'text-warning-600 dark:text-warning-400'
                    : 'text-gray-900 dark:text-gray-100'
                }`}>
                  {selectedSale.payment_method === 'آجل' ? 'آجل' :
                   selectedSale.payment_method === 'جزئي' ? 'جزئي' :
                   selectedSale.payment_method === 'cash' ? 'نقدي' : 'بطاقة'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">حالة الدفع:</p>
                <p className={`font-medium ${
                  selectedSale.payment_status === 'paid' ? 'text-success-600 dark:text-success-400' :
                  selectedSale.payment_status === 'partial' ? 'text-warning-600 dark:text-warning-400' :
                  'text-danger-600 dark:text-danger-400'
                }`}>
                  {selectedSale.payment_status === 'paid' ? 'مدفوع كاملاً' :
                   selectedSale.payment_status === 'partial' ? 'مدفوع جزئياً' :
                   'غير مدفوع'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">الكاشير:</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">{selectedSale.cashier_name || '-'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">المبلغ المدفوع فعلياً:</p>
                <p className="font-medium text-success-600 dark:text-success-400">
                  <FormattedCurrency amount={selectedSale.amount_paid} />
                </p>
              </div>
              {(selectedSale.debt_amount || 0) > 0 && (
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">قيمة الدين:</p>
                  <p className="font-medium text-danger-600 dark:text-danger-400">
                    <FormattedCurrency amount={selectedSale.debt_amount!} />
                  </p>
                  {selectedSale.customer_name && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">على: {selectedSale.customer_name}</p>
                  )}
                </div>
              )}
              {selectedSale.payment_method === 'cash' && selectedSale.change_amount > 0 && (
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">المتبقي:</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    <FormattedCurrency amount={selectedSale.change_amount} />
                  </p>
                </div>
              )}
            </div>

            <div className="border-t dark:border-gray-700 pt-4 mb-6">
              <h4 className="font-medium mb-3 text-gray-900 dark:text-gray-100">المنتجات:</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المنتج
                      </th>
                      <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الكمية
                      </th>
                      <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        السعر
                      </th>
                      <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المجموع
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {selectedSale.items.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                          {item.product_name}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {item.quantity}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          <FormattedCurrency amount={item.price} />
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <FormattedCurrency amount={item.subtotal} />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    {/* سعر المنتجات */}
                    <tr className="bg-gray-50 dark:bg-gray-700">
                      <td colSpan={3} className="px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                        إجمالي سعر المنتجات:
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        <FormattedCurrency amount={selectedSale.total_amount} />
                        <div className="text-xs text-gray-500 dark:text-gray-400">قبل الخصم والضريبة</div>
                      </td>
                    </tr>

                    {/* الخصم */}
                    {selectedSale.discount_amount > 0 && (
                      <tr className="bg-orange-50 dark:bg-orange-900/20">
                        <td colSpan={3} className="px-4 py-2 text-sm font-medium text-orange-700 dark:text-orange-300 text-right">
                          الخصم ({selectedSale.discount_type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}):
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-orange-700 dark:text-orange-300">
                          -<FormattedCurrency amount={selectedSale.discount_amount} />
                          <div className="text-xs text-orange-600 dark:text-orange-400">
                            {selectedSale.discount_type === 'percentage' ? (
                              `(${((selectedSale.discount_amount / selectedSale.total_amount) * 100).toFixed(1)}% من سعر المنتجات)`
                            ) : (
                              '(مبلغ ثابت)'
                            )}
                          </div>
                        </td>
                      </tr>
                    )}

                    {/* المجموع بعد الخصم */}
                    <tr className="bg-gray-50 dark:bg-gray-700">
                      <td colSpan={3} className="px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                        المجموع بعد الخصم:
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        <FormattedCurrency amount={selectedSale.total_amount - (selectedSale.discount_amount || 0)} />
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formatNumber(selectedSale.total_amount)} - {formatNumber(selectedSale.discount_amount || 0)}
                        </div>
                      </td>
                    </tr>

                    {/* الضريبة */}
                    {selectedSale.tax_amount > 0 && (
                      <tr className="bg-blue-50 dark:bg-blue-900/20">
                        <td colSpan={3} className="px-4 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 text-right">
                          الضريبة:
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-blue-700 dark:text-blue-300">
                          +<FormattedCurrency amount={selectedSale.tax_amount} />
                          <div className="text-xs text-blue-600 dark:text-blue-400">
                            {(() => {
                              const totalAfterDiscount = selectedSale.total_amount - (selectedSale.discount_amount || 0);
                              const taxRate = totalAfterDiscount > 0 ? ((selectedSale.tax_amount / totalAfterDiscount) * 100) : 0;
                              return `(${taxRate.toFixed(1)}% من المبلغ بعد الخصم)`;
                            })()}
                          </div>
                        </td>
                      </tr>
                    )}

                    {/* المجموع النهائي للفاتورة */}
                    <tr className="bg-gray-100 dark:bg-gray-600 border-t-2 border-gray-300 dark:border-gray-500">
                      <td colSpan={3} className="px-4 py-3 text-base font-bold text-gray-700 dark:text-gray-300 text-right">
                        إجمالي قيمة الفاتورة:
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-base font-bold text-gray-700 dark:text-gray-300">
                        {(() => {
                          const discountAmount = selectedSale.discount_amount || 0;
                          const totalAfterDiscount = selectedSale.total_amount - discountAmount;
                          const taxAmount = selectedSale.tax_amount || 0;
                          const finalTotal = totalAfterDiscount + taxAmount;
                          return <FormattedCurrency amount={finalTotal} />;
                        })()}
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formatNumber(selectedSale.total_amount - (selectedSale.discount_amount || 0))} + {formatNumber(selectedSale.tax_amount || 0)}
                        </div>
                      </td>
                    </tr>

                    {/* المبلغ المدفوع فعلياً */}
                    <tr className="bg-success-50 dark:bg-success-900/20">
                      <td colSpan={3} className="px-4 py-3 text-base font-bold text-success-700 dark:text-success-300 text-right">
                        المبلغ المدفوع فعلياً:
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-base font-bold text-success-700 dark:text-success-300">
                        <FormattedCurrency amount={selectedSale.amount_paid} />
                        <div className="text-xs text-success-600 dark:text-success-400">
                          {selectedSale.payment_status === 'paid' ? 'مدفوع كاملاً' :
                           selectedSale.payment_status === 'partial' ? 'مدفوع جزئياً' :
                           'غير مدفوع'}
                        </div>
                      </td>
                    </tr>

                    {/* قيمة الدين إن وجدت */}
                    {(selectedSale.debt_amount || 0) > 0 && (
                      <tr className="bg-danger-50 dark:bg-danger-900/20">
                        <td colSpan={3} className="px-4 py-3 text-base font-bold text-danger-700 dark:text-danger-300 text-right">
                          قيمة الدين المستحق:
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-base font-bold text-danger-700 dark:text-danger-300">
                          <FormattedCurrency amount={selectedSale.debt_amount!} />
                          <div className="text-xs text-danger-600 dark:text-danger-400">
                            {selectedSale.customer_name ? `على: ${selectedSale.customer_name}` : 'مبلغ مستحق'}
                          </div>
                        </td>
                      </tr>
                    )}
                  </tfoot>
                </table>
              </div>
            </div>

            <div className="flex justify-between border-t dark:border-gray-700 pt-4">
              <button
                onClick={() => printReceipt(selectedSale.id)}
                className="bg-success-600 hover:bg-success-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-success-600 hover:border-success-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-success-500/20 shadow-lg hover:shadow-xl gap-2"
              >
                <FaPrint />
                <span>طباعة الفاتورة</span>
              </button>

              <button
                onClick={() => setShowSaleDetail(false)}
                className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sales;