import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaPlus,
  FaTrash,
  FaEdit,
  FaKey,
  FaArrowLeft,
  FaSync,
  FaUserShield,
  FaUserCog,
  FaSearch,
  FaCheck,
  FaTimes,
  FaExclamationTriangle,
  FaUsers,
  FaUser,
  FaFilter,
  FaChevronLeft,
  FaChevronRight,
  FaTrophy,
  FaMedal,
  FaStar,
  FaThumbsUp,
  FaSmile,
  FaMeh,
  FaFrown,
  FaSortAmountDown
} from 'react-icons/fa';
import api from '../lib/axios';
import { useAuthStore } from '../stores/authStore';
import { useTheme } from '../contexts/ThemeContext';
import ToggleSwitch from '../components/ToggleSwitch';
import Modal from '../components/Modal';
import SimpleConfirmModal from '../components/SimpleConfirmModal';
import { TextInput, SelectInput } from '../components/inputs';

interface User {
  id: number;
  username: string;
  full_name: string;
  email: string | null;
  role: 'admin' | 'cashier';
  is_active: boolean;
  created_at: string;
  sales_count?: number;
}

interface UserFormData {
  username: string;
  full_name: string;
  email: string;
  password: string;
  confirm_password: string;
  role: 'admin' | 'cashier';
  is_active: boolean;
}

const initialFormData: UserFormData = {
  username: '',
  full_name: '',
  email: '',
  password: '',
  confirm_password: '',
  role: 'cashier',
  is_active: true
};

const Users: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [formData, setFormData] = useState<UserFormData>(initialFormData);
  const [showPassword, setShowPassword] = useState(false);
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const [showInactiveOnly, setShowInactiveOnly] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>('');

  // Pagination states
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [displayedUsers, setDisplayedUsers] = useState<User[]>([]);

  // Stats state
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    adminUsers: 0,
    cashierUsers: 0
  });

  const { user: currentUser } = useAuthStore();
  useTheme(); // Usar el hook para asegurar que los estilos dark mode se apliquen correctamente
  const navigate = useNavigate();

  // Get sales icon and color based on ranking
  const getSalesIconAndColor = (salesCount: number, userRank: number) => {
    if (salesCount === 0) {
      return {
        icon: <FaFrown />,
        bgColor: 'bg-gray-100 dark:bg-gray-700',
        textColor: 'text-gray-600 dark:text-gray-400',
        label: 'لا توجد مبيعات'
      };
    }

    switch (userRank) {
      case 1: // المركز الأول - كأس ذهبي
        return {
          icon: <FaTrophy />,
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
          textColor: 'text-yellow-800 dark:text-yellow-300',
          label: 'الأول'
        };
      case 2: // المركز الثاني - ميدالية فضية
        return {
          icon: <FaMedal />,
          bgColor: 'bg-gray-100 dark:bg-gray-700',
          textColor: 'text-gray-700 dark:text-gray-300',
          label: 'الثاني'
        };
      case 3: // المركز الثالث - ميدالية برونزية
        return {
          icon: <FaMedal />,
          bgColor: 'bg-orange-100 dark:bg-orange-900/30',
          textColor: 'text-orange-700 dark:text-orange-300',
          label: 'الثالث'
        };
      default:
        if (salesCount >= 20) {
          return {
            icon: <FaStar />,
            bgColor: 'bg-purple-100 dark:bg-purple-900/30',
            textColor: 'text-purple-700 dark:text-purple-300',
            label: 'ممتاز'
          };
        } else if (salesCount >= 10) {
          return {
            icon: <FaThumbsUp />,
            bgColor: 'bg-green-100 dark:bg-green-900/30',
            textColor: 'text-green-700 dark:text-green-300',
            label: 'جيد جداً'
          };
        } else if (salesCount >= 5) {
          return {
            icon: <FaSmile />,
            bgColor: 'bg-blue-100 dark:bg-blue-900/30',
            textColor: 'text-blue-700 dark:text-blue-300',
            label: 'جيد'
          };
        } else {
          return {
            icon: <FaMeh />,
            bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
            textColor: 'text-yellow-600 dark:text-yellow-400',
            label: 'مقبول'
          };
        }
    }
  };

  // Calculate user rankings based on sales count
  const getUserRankings = () => {
    const sortedUsers = [...users].sort((a, b) => (b.sales_count || 0) - (a.sales_count || 0));
    const rankings: Record<number, number> = {};

    sortedUsers.forEach((user, index) => {
      if (user.sales_count && user.sales_count > 0) {
        rankings[user.id] = index + 1;
      }
    });

    return rankings;
  };

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();

    // Check if user has admin role
    if (currentUser?.role !== 'admin') {
      setErrorMessage('لا تملك صلاحيات كافية للوصول إلى هذه الصفحة');
    }
  }, [currentUser]);

  // Update stats when users change
  useEffect(() => {
    fetchStats();
  }, [users]);

  // Filter users when search term or filters change
  useEffect(() => {
    applyFilters();
  }, [searchTerm, users, showActiveOnly, showInactiveOnly, selectedRole]);

  // Update displayed users when filtered users or pagination changes
  useEffect(() => {
    updateDisplayedUsers();
  }, [filteredUsers, currentPage, itemsPerPage]);

  // Calculate stats from users data
  const fetchStats = () => {
    setStats({
      totalUsers: users.length,
      activeUsers: users.filter(u => u.is_active).length,
      adminUsers: users.filter(u => u.role === 'admin').length,
      cashierUsers: users.filter(u => u.role === 'cashier').length
    });
  };

  // Apply filters to users
  const applyFilters = () => {
    let filtered = [...users];

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filters
    if (showActiveOnly && !showInactiveOnly) {
      filtered = filtered.filter(user => user.is_active);
    } else if (showInactiveOnly && !showActiveOnly) {
      filtered = filtered.filter(user => !user.is_active);
    }

    // Apply role filter
    if (selectedRole) {
      filtered = filtered.filter(user => user.role === selectedRole);
    }

    // Sort by sales count (highest first)
    filtered.sort((a, b) => (b.sales_count || 0) - (a.sales_count || 0));

    setFilteredUsers(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Update displayed users based on pagination
  const updateDisplayedUsers = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const usersToDisplay = filteredUsers.slice(startIndex, endIndex);
    setDisplayedUsers(usersToDisplay);
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  // Handle search
  const handleSearch = () => {
    applyFilters();
  };

  // Fetch users from API
  const fetchUsers = async () => {
    // Check if user has admin role first
    if (currentUser?.role !== 'admin') {
      console.log('User is not admin:', currentUser?.role);
      setErrorMessage('لا تملك صلاحيات كافية للوصول إلى هذه الصفحة');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      console.log('Fetching users...');
      console.log('Current auth token:', useAuthStore.getState().token);

      // Add a timestamp to prevent caching
      const response = await api.get('/api/users/', {
        params: { _t: new Date().getTime() }
      });
      console.log('Users fetched successfully:', response.data);
      setUsers(response.data);
      setFilteredUsers(response.data);
      setIsLoading(false);
    } catch (error: any) {
      console.error('Error fetching users:', error);

      if (error.response) {
        console.error('Error response:', error.response.status, error.response.data);
        if (error.response.status === 401) {
          console.log('Authentication error - token may be invalid');
          setErrorMessage('خطأ في المصادقة. يرجى تسجيل الخروج وإعادة تسجيل الدخول.');
        } else if (error.response.status === 403) {
          console.log('Authorization error - user may not have admin role');
          setErrorMessage('لا تملك صلاحيات كافية للوصول إلى هذه الصفحة');
        } else {
          console.log('Other API error:', error.response.status);
          setErrorMessage(`فشل في تحميل المستخدمين. يرجى المحاولة مرة أخرى. (${error.response.status})`);
        }
      } else if (error.request) {
        console.error('Error request - no response received:', error.request);
        setErrorMessage('فشل في الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.');
      } else {
        console.error('Error setting up request:', error.message);
        setErrorMessage('فشل في تحميل المستخدمين. يرجى المحاولة مرة أخرى.');
      }

      setIsLoading(false);
    }
  };

  // Validate form before submit
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'الاسم الكامل مطلوب';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صالح';
    }

    if (!isEditMode) {
      if (!formData.password) {
        newErrors.password = 'كلمة المرور مطلوبة';
      } else if (formData.password.length < 6) {
        newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      if (formData.password !== formData.confirm_password) {
        newErrors.confirm_password = 'كلمات المرور غير متطابقة';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Validate password reset form
  const validatePasswordReset = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!newPassword) {
      newErrors.newPassword = 'كلمة المرور الجديدة مطلوبة';
    } else if (newPassword.length < 6) {
      newErrors.newPassword = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    if (newPassword !== confirmNewPassword) {
      newErrors.confirmNewPassword = 'كلمات المرور غير متطابقة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Reset form to initial state
  const resetForm = () => {
    setFormData(initialFormData);
    setErrors({});
    setIsEditMode(false);
    setSelectedUserId(null);
    setShowForm(false);
  };

  // Open modal to create new user
  const handleNewUser = () => {
    resetForm();
    setShowForm(true);
  };

  // Open modal to edit user
  const handleEditUser = (user: User) => {
    setIsEditMode(true);
    setSelectedUserId(user.id);
    setFormData({
      username: user.username,
      full_name: user.full_name,
      email: user.email || '',
      password: '',
      confirm_password: '',
      role: user.role,
      is_active: user.is_active
    });
    setShowForm(true);
  };

  // Open modal to reset user password
  const handleResetPassword = (userId: number) => {
    setSelectedUserId(userId);
    setNewPassword('');
    setConfirmNewPassword('');
    setShowResetPassword(true);
  };

  // Format date with western numerals in DD/MM/YYYY format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  // Delete user
  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setShowDeleteModal(true);
  };

  // Submit user form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (isEditMode && selectedUserId) {
        // Update user
        const updateData = {
          username: formData.username,
          full_name: formData.full_name,
          email: formData.email || null,
          role: formData.role,
          is_active: formData.is_active
        };

        // Usar POST en lugar de PUT ya que el backend no tiene un endpoint PUT para /api/users/{id}
        await api.post(`/api/users/${selectedUserId}/update`, updateData);
        setSuccessMessage('تم تحديث المستخدم بنجاح');
      } else {
        // Create new user
        const userData = {
          username: formData.username,
          full_name: formData.full_name,
          email: formData.email || null,
          password: formData.password,
          role: formData.role,
          is_active: formData.is_active
        };

        await api.post('/api/users', userData);
        setSuccessMessage('تم إنشاء المستخدم بنجاح');
      }

      // Refresh users list
      fetchUsers();
      resetForm();

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error: any) {
      console.error('Error saving user:', error);

      if (error.response && error.response.data && error.response.data.detail) {
        if (error.response.data.detail.includes('already exists')) {
          setErrors({ ...errors, username: 'اسم المستخدم موجود بالفعل' });
        } else {
          setErrorMessage(`فشل في حفظ المستخدم: ${error.response.data.detail}`);
        }
      } else {
        setErrorMessage('فشل في حفظ المستخدم. يرجى المحاولة مرة أخرى.');
      }

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Submit password reset
  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePasswordReset()) {
      return;
    }

    try {
      // Usar POST en lugar de PUT para mantener consistencia con el endpoint de actualización
      await api.post(`/api/users/${selectedUserId}/reset-password`, {
        password: newPassword
      });

      setShowResetPassword(false);
      setSuccessMessage('تم تغيير كلمة المرور بنجاح');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error resetting password:', error);
      setErrorMessage('فشل في تغيير كلمة المرور. يرجى المحاولة مرة أخرى.');

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Confirm delete user
  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await api.delete(`/api/users/${userToDelete.id}`);
      setUsers(users.filter(user => user.id !== userToDelete.id));
      setSuccessMessage('تم حذف المستخدم بنجاح');
      setTimeout(() => setSuccessMessage(''), 3000);
      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (error: any) {
      console.error('Error deleting user:', error);

      // Handle specific error messages from backend
      let errorMsg = 'حدث خطأ أثناء حذف المستخدم';

      if (error.response?.data?.detail) {
        const detail = error.response.data.detail;

        // Check if it's a constraint error (user has sales records)
        if (detail.includes('sales records')) {
          errorMsg = `لا يمكن حذف المستخدم "${userToDelete.full_name}". هذا المستخدم لديه سجلات مبيعات في النظام. يرجى نقل أو إزالة هذه السجلات قبل حذف المستخدم.`;
        } else if (detail.includes('Cannot delete your own account')) {
          errorMsg = 'لا يمكنك حذف حسابك الشخصي';
        } else {
          errorMsg = detail;
        }
      }

      setErrorMessage(errorMsg);
      setTimeout(() => setErrorMessage(''), 8000); // Longer timeout for detailed messages
      setShowDeleteModal(false);
      setUserToDelete(null);
    }
  };

  // Check if user has admin role
  if (currentUser?.role !== 'admin') {
    return (
      <div className="touch-container">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/')}
              className="btn-icon mr-3"
            >
              <FaArrowLeft />
            </button>
            <h1 className="text-2xl font-bold text-secondary-900">إدارة المستخدمين</h1>
          </div>
        </div>

        {/* Error Message */}
        <div className="bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-4 rounded-xl mb-4 shadow-soft flex items-center">
          <FaExclamationTriangle className="ml-2" />
          لا تملك صلاحيات كافية للوصول إلى هذه الصفحة. يرجى تسجيل الدخول كمدير.
        </div>
      </div>
    );
  }

  return (
    <div className="touch-container">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaUsers className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المستخدمين</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة حسابات المستخدمين والصلاحيات
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap">
              <button
                onClick={() => {
                  fetchUsers();
                  fetchStats();
                }}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 shadow-lg hover:shadow-xl"
                title="تحديث"
              >
                <FaSync className={`text-sm ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={handleNewUser}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FaPlus className="ml-2 text-sm" />
                <span className="hidden sm:inline lg:inline">إضافة مستخدم</span>
                <span className="sm:hidden lg:hidden">إضافة</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      {successMessage && (
        <div className="bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300 p-4 rounded-xl mb-4 shadow-soft flex items-center">
          <FaCheck className="ml-2" />
          {successMessage}
        </div>
      )}

      {errorMessage && (
        <div className="bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-4 rounded-xl mb-4 shadow-soft flex items-center">
          <FaExclamationTriangle className="ml-2" />
          {errorMessage}
        </div>
      )}

      {/* Stats Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Users */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUsers className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">إجمالي المستخدمين</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{stats.totalUsers}</div>
            </div>
          </div>

          {/* Active Users */}
          <div className="flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30">
            <div className="bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUser className="text-success-600 dark:text-success-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-success-700 dark:text-success-300 mb-1">المستخدمين النشطين</div>
              <div className="text-xl font-bold text-success-600 dark:text-success-400">{stats.activeUsers}</div>
            </div>
          </div>

          {/* Admin Users */}
          <div className="flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-100 dark:border-purple-800/30">
            <div className="bg-purple-100 dark:bg-purple-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUserShield className="text-purple-600 dark:text-purple-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-1">المديرين</div>
              <div className="text-xl font-bold text-purple-600 dark:text-purple-400">{stats.adminUsers}</div>
            </div>
          </div>

          {/* Cashier Users */}
          <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUserCog className="text-blue-600 dark:text-blue-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">الكاشيرين</div>
              <div className="text-xl font-bold text-blue-600 dark:text-blue-400">{stats.cashierUsers}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>
            <button
              onClick={handleSearch}
              className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
            >
              <FaSearch className="ml-2" />
              بحث
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-6 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Items Per Page Filter */}
              <div className="w-full">
                <SelectInput
                  label="عدد العناصر"
                  name="itemsPerPage"
                  value={itemsPerPage.toString()}
                  onChange={(value: string) => handleItemsPerPageChange(parseInt(value, 10))}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                  placeholder="اختر عدد العناصر..."
                />
              </div>

              {/* Role Filter */}
              <div className="w-full">
                <SelectInput
                  label="الدور"
                  name="role"
                  value={selectedRole}
                  onChange={(value: string) => setSelectedRole(value)}
                  options={[
                    { value: '', label: 'جميع الأدوار' },
                    { value: 'admin', label: 'مدير' },
                    { value: 'cashier', label: 'كاشير' }
                  ]}
                  placeholder="اختر الدور..."
                />
              </div>

              {/* Active Status Filter */}
              <div className="w-full">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  المستخدمين النشطين
                </label>
                <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                  <ToggleSwitch
                    id="activeOnly"
                    checked={showActiveOnly}
                    onChange={(checked) => {
                      setShowActiveOnly(checked);
                      if (checked) setShowInactiveOnly(false);
                    }}
                    label="النشطين فقط"
                    className="w-full"
                  />
                </div>
              </div>

              {/* Inactive Status Filter */}
              <div className="w-full">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  المستخدمين غير النشطين
                </label>
                <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                  <ToggleSwitch
                    id="inactiveOnly"
                    checked={showInactiveOnly}
                    onChange={(checked) => {
                      setShowInactiveOnly(checked);
                      if (checked) setShowActiveOnly(false);
                    }}
                    label="غير النشطين فقط"
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Users Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden border border-gray-200 dark:border-gray-700">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-14 w-14 border-b-3 border-primary-600 dark:border-primary-400"></div>
          </div>
        ) : displayedUsers.length > 0 ? (
          <div className="overflow-x-auto custom-scrollbar">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المستخدم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    البريد الإلكتروني
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الدور
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <div className="flex items-center justify-end gap-1">
                      <span>عدد المبيعات</span>
                      <FaSortAmountDown className="text-primary-500 dark:text-primary-400" title="مرتب من الأعلى إلى الأقل" />
                    </div>
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    تاريخ الإنشاء
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {displayedUsers.map(user => (
                  <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                            <FaUser className="text-primary-600 dark:text-primary-400" />
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {user.full_name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            @{user.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-gray-100">
                        {user.email || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.role === 'admin' ? (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                          <FaUserShield className="ml-1" />
                          مدير
                        </span>
                      ) : (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                          <FaUserCog className="ml-1" />
                          كاشير
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.is_active
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      }`}>
                        {user.is_active ? (
                          <>
                            <FaCheck className="ml-1" />
                            نشط
                          </>
                        ) : (
                          <>
                            <FaTimes className="ml-1" />
                            غير نشط
                          </>
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {(() => {
                          const rankings = getUserRankings();
                          const userRank = rankings[user.id] || 0;
                          const salesCount = user.sales_count || 0;
                          const { icon, bgColor, textColor, label } = getSalesIconAndColor(salesCount, userRank);

                          return (
                            <div className="flex items-center gap-2">
                              <span
                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor} cursor-help`}
                                title={`${label} - ${salesCount} ${salesCount === 1 ? 'مبيعة' : 'مبيعات'}${userRank > 0 ? ` (المركز ${userRank})` : ''}`}
                              >
                                <span className="ml-1">{icon}</span>
                                {salesCount}
                              </span>
                              {userRank > 0 && userRank <= 3 && (
                                <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                                  {label}
                                </span>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {formatDate(user.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEditUser(user)}
                          className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"
                          title="تعديل"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleResetPassword(user.id)}
                          className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"
                          title="تغيير كلمة المرور"
                        >
                          <FaKey />
                        </button>
                        {currentUser?.id !== user.id && (
                          <button
                            onClick={() => handleDeleteUser(user)}
                            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                            title="حذف"
                          >
                            <FaTrash />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <FaUsers className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm ? 'لم يتم العثور على مستخدمين مطابقين' : 'لا يوجد مستخدمين'}
            </p>
          </div>
        )}

        {/* Pagination */}
        {filteredUsers.length > 0 && (
          <div className="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-6 py-3 border-2 border-gray-300 dark:border-gray-600 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 min-w-[100px]"
                >
                  السابق
                </button>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-6 py-3 border-2 border-gray-300 dark:border-gray-600 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 min-w-[100px]"
                >
                  التالي
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    <span className="font-medium">{currentPage}</span> / <span className="font-medium">{totalPages}</span>{' '}
                    <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {filteredUsers.length})</span>
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">السابق</span>
                      <FaChevronRight className="h-5 w-5" aria-hidden="true" />
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                      if (
                        page === 1 ||
                        page === totalPages ||
                        (page >= currentPage - 1 && page <= currentPage + 1)
                      ) {
                        return (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              page === currentPage
                                ? 'z-10 bg-primary-50 dark:bg-primary-900/30 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-300'
                                : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      } else if (
                        page === currentPage - 2 ||
                        page === currentPage + 2
                      ) {
                        return (
                          <span
                            key={page}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            ...
                          </span>
                        );
                      }
                      return null;
                    })}

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">التالي</span>
                      <FaChevronLeft className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* User Form Modal */}
      <Modal
        isOpen={showForm}
        onClose={resetForm}
        title={isEditMode ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}
        size="md"
      >
        <form onSubmit={handleSubmit} className="space-y-5">
          {/* Basic Information Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaUser className="ml-2 text-primary-600 dark:text-primary-400" />
              المعلومات الأساسية
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <TextInput
                label="اسم المستخدم"
                name="username"
                value={formData.username}
                onChange={(value) => {
                  setFormData({ ...formData, username: value });
                  if (errors.username) {
                    setErrors({ ...errors, username: '' });
                  }
                }}
                placeholder="أدخل اسم المستخدم"
                required
                error={errors.username}
                icon={<FaUser />}
              />

              <TextInput
                label="الاسم الكامل"
                name="full_name"
                value={formData.full_name}
                onChange={(value) => {
                  setFormData({ ...formData, full_name: value });
                  if (errors.full_name) {
                    setErrors({ ...errors, full_name: '' });
                  }
                }}
                placeholder="أدخل الاسم الكامل"
                required
                error={errors.full_name}
                icon={<FaUser />}
              />
            </div>

            <div className="mt-4">
              <TextInput
                label="البريد الإلكتروني"
                name="email"
                type="email"
                value={formData.email}
                onChange={(value) => {
                  setFormData({ ...formData, email: value });
                  if (errors.email) {
                    setErrors({ ...errors, email: '' });
                  }
                }}
                placeholder="أدخل البريد الإلكتروني"
                error={errors.email}
                dir="ltr"
              />
            </div>
          </div>

          {/* Password Section - Only for new users */}
          {!isEditMode && (
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <FaKey className="ml-2 text-primary-600 dark:text-primary-400" />
                كلمة المرور
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <TextInput
                  label="كلمة المرور"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(value) => {
                    setFormData({ ...formData, password: value });
                    if (errors.password) {
                      setErrors({ ...errors, password: '' });
                    }
                  }}
                  placeholder="أدخل كلمة المرور"
                  required
                  error={errors.password}
                  dir="ltr"
                  icon={<FaKey />}
                />

                <TextInput
                  label="تأكيد كلمة المرور"
                  name="confirm_password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.confirm_password}
                  onChange={(value) => {
                    setFormData({ ...formData, confirm_password: value });
                    if (errors.confirm_password) {
                      setErrors({ ...errors, confirm_password: '' });
                    }
                  }}
                  placeholder="أعد إدخال كلمة المرور"
                  required
                  error={errors.confirm_password}
                  dir="ltr"
                  icon={<FaKey />}
                />
              </div>

              <div className="mt-4 flex items-center">
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 flex items-center"
                >
                  {showPassword ? 'إخفاء كلمة المرور' : 'عرض كلمة المرور'}
                </button>
              </div>
            </div>
          )}

          {/* Role and Status Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaUserShield className="ml-2 text-primary-600 dark:text-primary-400" />
              الدور والحالة
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <SelectInput
                label="الدور"
                name="role"
                value={formData.role}
                onChange={(value) => setFormData({ ...formData, role: value as 'admin' | 'cashier' })}
                options={[
                  { value: 'admin', label: 'مدير' },
                  { value: 'cashier', label: 'كاشير' }
                ]}
                icon={<FaUserShield />}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  حالة المستخدم
                </label>
                <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                  <ToggleSwitch
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(checked) => setFormData({ ...formData, is_active: checked })}
                    label="المستخدم نشط"
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={resetForm}
              className="px-6 py-2.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium"
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="px-6 py-2.5 bg-primary-600 text-white rounded-xl hover:bg-primary-700 dark:hover:bg-primary-500 transition-colors font-medium"
            >
              {isEditMode ? 'تحديث المستخدم' : 'إضافة المستخدم'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Reset Password Modal */}
      <Modal
        isOpen={showResetPassword}
        onClose={() => setShowResetPassword(false)}
        title="تغيير كلمة المرور"
        size="sm"
      >
        <form onSubmit={handlePasswordReset} className="space-y-5">
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaKey className="ml-2 text-primary-600 dark:text-primary-400" />
              كلمة المرور الجديدة
            </h3>
            <div className="space-y-4">
              <TextInput
                label="كلمة المرور الجديدة"
                name="newPassword"
                type={showPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(value) => {
                  setNewPassword(value);
                  if (errors.newPassword) {
                    setErrors({ ...errors, newPassword: '' });
                  }
                }}
                placeholder="أدخل كلمة المرور الجديدة"
                required
                error={errors.newPassword}
                dir="ltr"
                icon={<FaKey />}
              />

              <TextInput
                label="تأكيد كلمة المرور الجديدة"
                name="confirmNewPassword"
                type={showPassword ? 'text' : 'password'}
                value={confirmNewPassword}
                onChange={(value) => {
                  setConfirmNewPassword(value);
                  if (errors.confirmNewPassword) {
                    setErrors({ ...errors, confirmNewPassword: '' });
                  }
                }}
                placeholder="أعد إدخال كلمة المرور الجديدة"
                required
                error={errors.confirmNewPassword}
                dir="ltr"
                icon={<FaKey />}
              />

              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 flex items-center"
                >
                  {showPassword ? 'إخفاء كلمة المرور' : 'عرض كلمة المرور'}
                </button>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowResetPassword(false)}
              className="px-6 py-2.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium"
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="px-6 py-2.5 bg-primary-600 text-white rounded-xl hover:bg-primary-700 dark:hover:bg-primary-500 transition-colors font-medium"
            >
              تغيير كلمة المرور
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <SimpleConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDeleteUser}
        title="تأكيد حذف المستخدم"
        message={`هل أنت متأكد من حذف المستخدم "${userToDelete?.full_name}"؟ لا يمكن التراجع عن هذا الإجراء.`}
        confirmText="حذف المستخدم"
        cancelText="إلغاء"
      />
    </div>
  );
};

export default Users;