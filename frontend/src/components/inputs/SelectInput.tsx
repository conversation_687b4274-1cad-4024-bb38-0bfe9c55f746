import React, { useState, useEffect, useRef, forwardRef } from 'react';
import { FaChevronDown, FaCheck, FaExclamationTriangle, FaCheckCircle, FaSearch } from 'react-icons/fa';

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

interface SelectInputProps {
  label?: string;
  name: string;
  id?: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  success?: string;
  icon?: React.ReactNode;
  className?: string;
  searchable?: boolean;
  clearable?: boolean;
  maxHeight?: string;
}

const SelectInput = forwardRef<HTMLDivElement, SelectInputProps>(({
  label,
  name,
  id,
  value,
  onChange,
  options,
  placeholder = 'اختر خياراً...',
  required = false,
  disabled = false,
  error,
  success,
  icon,
  className = '',
  searchable = false,
  clearable = false,
  maxHeight = '200px'
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasInteracted, setHasInteracted] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  // Generate unique ID if not provided
  const selectId = id || `${name}-${Math.random().toString(36).substring(2, 11)}`;

  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get selected option
  const selectedOption = options.find(option => option.value === value);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setHasInteracted(true);
      setHighlightedIndex(-1);
    }
  };

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setSearchTerm('');
    setHasInteracted(true);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
    setHasInteracted(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    switch (e.key) {
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        break;
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleSelect(filteredOptions[highlightedIndex].value);
        }
        break;
    }
  };

  const showError = error && hasInteracted;
  const showSuccess = success && hasInteracted && !error;
  const isEmpty = !value;
  const showRequiredMessage = required && isEmpty && hasInteracted && !error;

  return (
    <div className={`relative ${className}`} ref={ref || selectRef}>
      {/* Label */}
      {label && (
        <label
          htmlFor={selectId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}

      {/* Select Container */}
      <div className="relative">
        {/* Icon */}
        {icon && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
            <div className={`transition-colors duration-200 ${
              showError
                ? 'text-red-500'
                : showSuccess
                  ? 'text-green-500'
                  : isOpen
                    ? 'text-primary-500'
                    : 'text-gray-400 dark:text-gray-500'
            }`}>
              {icon}
            </div>
          </div>
        )}

        {/* Select Button */}
        <button
          type="button"
          id={selectId}
          onClick={handleToggle}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className={`
            w-full rounded-xl border-2 h-12 px-4 text-right transition-all duration-200 ease-in-out flex items-center
            ${icon ? 'pr-12' : ''}
            ${clearable && value ? 'pl-12' : 'pl-10'}
            ${disabled
              ? 'bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700'
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 cursor-pointer'
            }
            ${showError
              ? 'border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20'
              : showSuccess
                ? 'border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20'
                : isOpen
                  ? 'border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }
            focus:outline-none
          `}
        >
          <span className={`block truncate ${!selectedOption ? 'text-gray-400 dark:text-gray-500' : ''}`}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
        </button>

        {/* Dropdown Arrow */}
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FaChevronDown className={`h-4 w-4 text-gray-400 dark:text-gray-500 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} />
        </div>

        {/* Clear Button */}
        {clearable && value && !disabled && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute inset-y-0 left-8 flex items-center pl-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <span className="text-lg">×</span>
          </button>
        )}

        {/* Status Icon */}
        {(showError || showSuccess) && (
          <div className="absolute inset-y-0 left-0 flex items-center pl-3">
            {showError ? (
              <FaExclamationTriangle className="h-5 w-5 text-red-500" />
            ) : (
              <FaCheckCircle className="h-5 w-5 text-green-500" />
            )}
          </div>
        )}

        {/* Hidden input for form submission */}
        <input type="hidden" name={name} value={value || ''} />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Search Input */}
          {searchable && (
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-4 w-4" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="بحث..."
                  className="w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          )}

          {/* Options */}
          <div
            className="max-h-60 overflow-auto dropdown-scrollbar"
            style={{ maxHeight }}
          >
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-center">
                لا توجد خيارات متاحة
              </div>
            ) : (
              filteredOptions.map((option, index) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => !option.disabled && handleSelect(option.value)}
                  disabled={option.disabled}
                  className={`w-full px-3 py-2 text-right flex items-center justify-between transition-colors ${
                    option.disabled
                      ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                      : index === highlightedIndex
                        ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300'
                        : option.value === value
                          ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-200'
                          : 'text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center">
                    {option.icon && <span className="ml-2">{option.icon}</span>}
                    <span className="truncate">{option.label}</span>
                  </div>
                  {option.value === value && (
                    <FaCheck className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                  )}
                </button>
              ))
            )}
          </div>


        </div>
      )}

      {/* Error Message */}
      {showError && (
        <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-red-500" />
            {error}
          </p>
        </div>
      )}

      {/* Success Message */}
      {showSuccess && (
        <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-green-700 dark:text-green-300 text-sm flex items-center">
            <FaCheckCircle className="ml-2 flex-shrink-0 text-green-500" />
            {success}
          </p>
        </div>
      )}

      {/* Required Message */}
      {showRequiredMessage && (
        <div className="mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
          <p className="text-orange-700 dark:text-orange-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-orange-500" />
            هذا الحقل مطلوب، يرجى اختيار قيمة
          </p>
        </div>
      )}
    </div>
  );
});

SelectInput.displayName = 'SelectInput';

export default SelectInput;
